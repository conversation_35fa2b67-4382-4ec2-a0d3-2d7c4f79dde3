# 全局错误监控和上报系统

## 系统概述

实现了一个完整的全局错误监控和上报系统，能够自动捕获小程序运行时错误并上报到服务器，帮助开发者及时发现和解决问题。

## 核心功能

### 1. 自动错误捕获
- **小程序错误**: 自动捕获 `wx.onError` 错误
- **Promise拒绝**: 自动捕获未处理的Promise拒绝
- **内存警告**: 监控内存不足警告
- **手动上报**: 支持手动上报自定义错误

### 2. 错误信息收集
- **错误详情**: 错误类型、消息、堆栈信息
- **系统信息**: 设备型号、系统版本、微信版本
- **用户信息**: 用户ID、登录状态
- **应用信息**: 小程序版本、启动场景、当前页面
- **上下文信息**: 自定义上下文数据

### 3. 智能上报机制
- **队列管理**: 错误排队上报，避免网络拥堵
- **重试机制**: 上报失败自动重试，最多3次
- **批量处理**: 支持批量上报多个错误
- **容错设计**: 上报失败不影响主要功能

## 文件结构

```
utils/error/
├── errorReporter.js     # 核心错误监控类
utils/api/
├── errorApi.js          # 错误上报API接口
config/
├── apiConfig.js         # API配置（已添加错误上报接口）
docs/
├── error-reporting-system.md  # 本文档
```

## 使用方法

### 1. 自动监控（已启用）

系统在应用启动时自动初始化，无需额外配置：

```javascript
// app.js 中已自动初始化
const errorReporter = require('./utils/error/errorReporter');
```

### 2. 手动上报错误

在任何页面或组件中手动上报错误：

```javascript
// 方法1: 通过全局App实例
const app = getApp();
app.reportError('custom_error', '自定义错误信息', {
  module: 'user_action',
  action: 'button_click'
});

// 方法2: 直接使用errorReporter
const errorReporter = require('../../utils/error/errorReporter');
errorReporter.reportManualError('api_error', error, {
  api_url: '/user/login',
  response_code: 500
});
```

### 3. 在API调用中使用

```javascript
// 在API请求失败时上报
try {
  const result = await userApi.login(code);
} catch (error) {
  // 上报API错误
  const app = getApp();
  app.reportError('api_error', error, {
    api: 'user_login',
    code: code
  });
  throw error;
}
```

### 4. 在页面错误处理中使用

```javascript
Page({
  onLoad() {
    try {
      // 页面初始化逻辑
      this.initPage();
    } catch (error) {
      // 上报页面初始化错误
      const app = getApp();
      app.reportError('page_init_error', error, {
        page: 'makeResume',
        step: 'initialization'
      });
    }
  }
});
```

## 错误报告格式

系统会自动收集以下信息并上报：

```json
{
  "error_type": "miniprogram_error",
  "error_message": "Cannot read property 'xxx' of undefined",
  "error_stack": "Error stack trace...",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "local_time": "2024/1/15 18:30:00",
  "system_info": {
    "platform": "ios",
    "system": "iOS 16.1.1",
    "model": "iPhone 14",
    "brand": "Apple",
    "wechat_version": "8.0.32"
  },
  "user_info": {
    "user_id": "user123",
    "has_token": true,
    "is_logged_in": true
  },
  "app_info": {
    "version": "1.0.0",
    "scene": "1001",
    "path": "pages/makeResume/makeResume"
  },
  "context": {
    "type": "runtime_error",
    "custom_data": "..."
  }
}
```

## 服务端接口要求

### 1. 错误上报接口

**接口地址**: `POST /error/report`

**请求参数**: 错误报告对象（如上所示）

**响应格式**:
```json
{
  "success": true,
  "message": "错误上报成功",
  "data": {
    "report_id": "error_123456",
    "timestamp": "2024-01-15T10:30:00.000Z"
  }
}
```

### 2. 批量上报接口（可选）

**接口地址**: `POST /error/report/batch`

**请求参数**:
```json
{
  "errors": [错误报告对象数组],
  "batch_size": 5,
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

## 配置选项

### 1. 启用/禁用错误上报

```javascript
const errorReporter = require('./utils/error/errorReporter');

// 禁用错误上报（比如在开发环境）
errorReporter.setEnabled(false);

// 启用错误上报
errorReporter.setEnabled(true);
```

### 2. 查看队列状态

```javascript
const status = errorReporter.getQueueStatus();
console.log('错误上报状态:', status);
// 输出: { queue_length: 2, is_reporting: false, is_enabled: true }
```

### 3. 清空错误队列

```javascript
errorReporter.clearQueue();
```

## 最佳实践

### 1. 错误分类

建议使用统一的错误类型命名：

```javascript
// API相关错误
app.reportError('api_error', error, { api: 'user_login' });

// 页面相关错误
app.reportError('page_error', error, { page: 'makeResume' });

// 数据相关错误
app.reportError('data_error', error, { operation: 'save_resume' });

// 用户操作错误
app.reportError('user_action_error', error, { action: 'generate_pdf' });
```

### 2. 上下文信息

提供有用的上下文信息帮助调试：

```javascript
app.reportError('form_validation_error', error, {
  form_type: 'basic_info',
  field_name: 'phone',
  field_value: 'invalid_phone',
  validation_rule: 'phone_format'
});
```

### 3. 避免敏感信息

不要在错误报告中包含敏感信息：

```javascript
// ❌ 错误：包含敏感信息
app.reportError('login_error', error, {
  password: 'user_password',  // 不要包含密码
  token: 'sensitive_token'    // 不要包含完整token
});

// ✅ 正确：只包含必要信息
app.reportError('login_error', error, {
  username_length: username.length,
  has_password: !!password,
  token_prefix: token ? token.substring(0, 8) + '...' : null
});
```

## 监控和分析

### 1. 服务端日志分析

建议在服务端对错误报告进行分析：

- 按错误类型统计
- 按设备型号统计
- 按页面路径统计
- 按时间段统计

### 2. 告警机制

建议设置告警规则：

- 错误频率超过阈值时告警
- 新类型错误出现时告警
- 特定页面错误率过高时告警

## 注意事项

1. **性能影响**: 错误监控对性能影响很小，但建议在生产环境中监控队列大小
2. **网络消耗**: 错误上报会消耗少量网络流量，已优化为后台上报
3. **存储空间**: 错误队列有大小限制（50条），超出会自动清理旧错误
4. **隐私保护**: 不会收集用户的个人敏感信息，只收集技术相关信息

## 测试验证

### 1. 手动触发错误测试

```javascript
// 在控制台中测试
const app = getApp();
app.reportError('test_error', '这是一个测试错误', { test: true });
```

### 2. 模拟网络错误

```javascript
// 测试网络失败重试机制
// 可以暂时断网后恢复网络，观察错误是否正常上报
```

### 3. 查看控制台日志

正常运行时应该看到：
```
✅ 全局错误监控已启动
✅ 错误监控系统已启动
```

发生错误时应该看到：
```
🚨 [miniprogram_error] 错误捕获: Error message
✅ 错误上报成功
```

## 总结

该错误监控系统提供了完整的错误收集、队列管理、自动上报功能，能够帮助开发者及时发现和解决线上问题，提升小程序的稳定性和用户体验。
