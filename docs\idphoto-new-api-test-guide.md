# 证件照新API测试指南

## 测试前准备

### 1. 服务端检查
确保证件照服务正常运行：
```bash
# 检查健康状态
curl -X GET "http://hivisionidphotosapi.gbw8848.cn/idphoto/health"

# 获取尺寸列表
curl -X GET "http://hivisionidphotosapi.gbw8848.cn/idphoto/sizes"

# 获取颜色列表
curl -X GET "http://hivisionidphotosapi.gbw8848.cn/idphoto/colors"
```

### 2. 用户登录状态
确保小程序中用户已登录并获得有效token。

## 测试步骤

### 第一阶段：主页面测试

#### 1.1 页面加载测试
- [ ] 打开证件照制作页面
- [ ] 验证显示"正在加载配置信息..."
- [ ] 验证加载完成后显示尺寸选项

#### 1.2 动态配置测试
- [ ] 验证尺寸选项从服务器动态获取
- [ ] 检查尺寸选项数据格式：
  ```javascript
  {
    name: "一寸",
    value: "one_inch", 
    width: 295,
    height: 413,
    print_size: "2.5cm*3.5cm",
    description: "标准一寸证件照"
  }
  ```
- [ ] 验证所有尺寸选项正常显示

#### 1.3 错误处理测试
- [ ] 断网状态下测试页面加载
- [ ] 验证错误提示和重试功能
- [ ] 验证返回按钮功能

### 第二阶段：结果页面测试

#### 2.1 照片生成测试
- [ ] 选择尺寸和照片后跳转到结果页面
- [ ] 验证显示"正在加载配置..."
- [ ] 验证显示"正在生成证件照..."
- [ ] 验证生成成功后显示证件照

#### 2.2 新API调用测试
验证API调用参数：
```javascript
{
  imagePath: "本地图片路径",
  size: "one_inch",
  color: "white"
}
```

验证API响应数据：
```javascript
{
  success: true,
  data: {
    image_base64: "生成的证件照base64",
    hd_image_base64: "高清版base64", 
    transparent_base64: "透明底base64",
    size: "one_inch",
    size_name: "一寸",
    color: "white",
    color_name: "白色",
    dimensions: {width: 295, height: 413}
  }
}
```

#### 2.3 背景色切换测试
- [ ] 验证颜色选项从服务器动态获取
- [ ] 测试切换不同背景色
- [ ] 验证每次切换都重新调用生成API
- [ ] 验证透明背景的特殊处理

#### 2.4 保存功能测试
- [ ] 测试保存标准版证件照
- [ ] 测试保存高清版证件照
- [ ] 验证相册权限处理

### 第三阶段：用户认证测试

#### 3.1 已登录用户测试
- [ ] 验证API请求携带正确的Authorization头
- [ ] 验证所有功能正常工作

#### 3.2 未登录用户测试
- [ ] 验证显示"用户未登录，请先登录"错误
- [ ] 验证引导用户登录

#### 3.3 Token过期测试
- [ ] 模拟token过期情况
- [ ] 验证错误处理和重新登录提示

### 第四阶段：性能和稳定性测试

#### 4.1 网络异常测试
- [ ] 弱网环境下的表现
- [ ] 网络中断后恢复的处理
- [ ] 超时处理机制

#### 4.2 并发请求测试
- [ ] 快速切换背景色时的处理
- [ ] 重复点击按钮的防抖处理

#### 4.3 大图片处理测试
- [ ] 测试大尺寸照片的处理
- [ ] 验证内存使用情况
- [ ] 验证处理时间

## 测试检查点

### API调用检查
```javascript
// 检查控制台日志
console.log('健康检查响应:', healthResult);
console.log('尺寸列表:', sizesResult);
console.log('颜色列表:', colorsResult);
console.log('证件照生成结果:', generateResult);
```

### 数据结构检查
```javascript
// 验证尺寸数据结构
sizeOptions.forEach(size => {
  console.assert(size.name, '尺寸名称不能为空');
  console.assert(size.value, '尺寸值不能为空');
  console.assert(size.width > 0, '宽度必须大于0');
  console.assert(size.height > 0, '高度必须大于0');
});

// 验证颜色数据结构
colorOptions.forEach(color => {
  console.assert(color.name, '颜色名称不能为空');
  console.assert(color.value, '颜色值不能为空');
  console.assert(color.hex, 'HEX值不能为空');
});
```

### 用户体验检查
- [ ] 加载状态提示清晰
- [ ] 错误信息用户友好
- [ ] 操作响应及时
- [ ] 界面布局正常

## 常见问题排查

### 1. 配置加载失败
**可能原因：**
- 服务器未启动
- 网络连接问题
- API地址配置错误

**排查步骤：**
1. 检查服务器状态
2. 验证API配置
3. 检查网络连接

### 2. 证件照生成失败
**可能原因：**
- 用户未登录
- Token过期
- 图片格式不支持
- 服务器处理错误

**排查步骤：**
1. 检查用户登录状态
2. 验证token有效性
3. 检查图片格式和大小
4. 查看服务器日志

### 3. 背景色切换异常
**可能原因：**
- 颜色配置获取失败
- API调用参数错误
- 网络请求失败

**排查步骤：**
1. 检查颜色配置数据
2. 验证API调用参数
3. 检查网络状态

## 测试完成标准

- [ ] 所有基础功能正常工作
- [ ] 动态配置获取成功
- [ ] 用户认证机制正常
- [ ] 错误处理完善
- [ ] 性能表现良好
- [ ] 用户体验流畅

## 测试报告模板

```
测试时间：____
测试环境：____
测试人员：____

功能测试结果：
□ 主页面加载：通过/失败
□ 配置获取：通过/失败  
□ 证件照生成：通过/失败
□ 背景色切换：通过/失败
□ 保存功能：通过/失败

性能测试结果：
□ 加载速度：____秒
□ 生成速度：____秒
□ 内存使用：正常/异常

发现问题：
1. ____
2. ____

建议改进：
1. ____
2. ____
```
