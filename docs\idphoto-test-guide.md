# 证件照功能测试指南

## 功能概述

证件照制作功能包含两个主要页面：
1. **证件照主页面** (`pages/idPhoto/idPhoto`) - 尺寸选择和照片上传
2. **证件照结果页面** (`pages/idPhoto/result/result`) - 处理结果展示和底色选择

## 测试步骤

### 1. 主页面测试

#### 1.1 页面访问测试
- [ ] 从主页点击"证件照制作"按钮
- [ ] 验证能正确跳转到证件照主页面
- [ ] 检查页面标题显示为"证件照制作"

#### 1.2 尺寸选择测试
- [ ] 验证显示6种证件照尺寸选项：
  - 一寸 (295×413像素, 25×35mm)
  - 二寸 (413×579像素, 35×49mm)
  - 大一寸 (390×567像素, 33×48mm)
  - 小一寸 (260×378像素, 22×32mm)
  - 大二寸 (449×661像素, 38×56mm)
  - 小二寸 (378×531像素, 32×45mm)
- [ ] 点击不同尺寸，验证选中状态切换正常
- [ ] 验证选中时有触觉反馈和视觉效果

#### 1.3 照片上传测试
- [ ] 点击上传区域，验证弹出选择照片界面
- [ ] 测试从相册选择照片
- [ ] 测试拍照功能
- [ ] 验证选择成功后显示预览图
- [ ] 验证可以重新选择照片

#### 1.4 开始制作测试
- [ ] 未选择尺寸时点击"开始制作"，验证提示"请选择尺寸和照片"
- [ ] 未选择照片时点击"开始制作"，验证提示"请选择尺寸和照片"
- [ ] 选择尺寸和照片后点击"开始制作"，验证正确跳转到结果页面

### 2. 结果页面测试

#### 2.1 照片处理测试
- [ ] 验证页面加载时显示"正在处理照片..."
- [ ] 验证能正确调用证件照生成API
- [ ] 验证处理成功后显示证件照预览
- [ ] 验证显示正确的尺寸信息

#### 2.2 底色选择测试
- [ ] 验证显示4种底色选项：
  - 透明
  - 白色
  - 蓝色
  - 红色
- [ ] 点击不同底色，验证能正确切换背景
- [ ] 验证切换时有加载状态提示
- [ ] 验证选中状态的视觉效果

#### 2.3 保存功能测试
- [ ] 点击"保存到相册"按钮
- [ ] 验证权限申请流程
- [ ] 验证保存成功提示
- [ ] 检查相册中是否有保存的证件照
- [ ] 如果有高清版本，验证同时保存标准版和高清版

#### 2.4 其他功能测试
- [ ] 点击"重新制作"按钮，验证返回主页面
- [ ] 长按证件照，验证显示保存菜单

### 3. 错误处理测试

#### 3.1 网络错误测试
- [ ] 断网状态下测试照片处理
- [ ] 验证显示网络错误提示
- [ ] 验证重试功能

#### 3.2 服务器错误测试
- [ ] 服务器未启动时测试
- [ ] 验证显示服务器连接失败提示
- [ ] 验证错误页面的返回功能

#### 3.3 权限错误测试
- [ ] 拒绝相册权限时测试保存功能
- [ ] 验证权限引导流程
- [ ] 验证设置页面跳转

### 4. 性能测试

#### 4.1 处理时间测试
- [ ] 测试不同大小照片的处理时间
- [ ] 验证超时处理机制
- [ ] 验证加载状态的准确性

#### 4.2 内存使用测试
- [ ] 测试大尺寸照片处理
- [ ] 验证内存释放
- [ ] 测试连续多次处理

## API配置检查

### 服务端API配置
确保在 `config/apiConfig.js` 中正确配置了证件照API：

```javascript
// 证件照相关API
idPhotoBaseUrl: 'http://127.0.0.1:8080',     // 证件照服务基础URL
idPhotoGenerateUrl: '/idphoto',              // 生成证件照(底透明)
idPhotoAddBackgroundUrl: '/add_background',  // 添加背景色
```

### 服务端启动
确保证件照服务端已启动：
```bash
python deploy_api.py
```

## 常见问题排查

1. **页面跳转失败**
   - 检查 `app.json` 中是否正确配置了页面路由
   - 检查页面文件是否存在

2. **API调用失败**
   - 检查服务端是否启动
   - 检查API配置是否正确
   - 查看控制台错误日志

3. **图片处理失败**
   - 检查图片格式是否支持
   - 检查图片大小是否合理
   - 检查网络连接

4. **保存失败**
   - 检查相册权限
   - 检查存储空间
   - 检查图片格式转换

## 测试完成标准

- [ ] 所有基础功能正常工作
- [ ] 错误处理机制完善
- [ ] 用户体验流畅
- [ ] 性能表现良好
- [ ] 兼容性测试通过
