<view class="container">
  <view class="formGroup">
    <!-- 经历时间 -->
    <view class="formItem">
      <text class="label">经历时间</text>
      <view class="datePicker">
        <view class="dateSection">
          <picker mode="date" fields="month" value="{{schoolEditFormData.startDate}}" data-field="startDate" bindchange="handleDateChange">
            <view class="picker {{schoolEditFormData.startDate ? '' : 'placeholder'}}">
              {{schoolEditFormData.startDate || '请选择'}}
            </view>
          </picker>
        </view>
        <text class="separator">至</text>
        <view class="dateSection">
          <picker mode="date" fields="month" value="{{schoolEditFormData.endDate === '至今' ? currentDate : schoolEditFormData.endDate}}" data-field="endDate" bindchange="handleDateChange">
            <view class="picker {{schoolEditFormData.endDate ? '' : 'placeholder'}}">
              {{schoolEditFormData.endDate || '请选择'}}
            </view>
          </picker>
        </view>
        <view class="nowBtn" bindtap="setEndDateToNow">至今</view>
      </view>
    </view>

    <!-- 担任角色 -->
    <view class="formItem">
      <text class="label">担任角色</text>
      <input class="input" placeholder="如社团、职务、奖学金、获奖、竞赛等" value="{{schoolEditFormData.role}}" data-field="role" bindinput="handleInput"/>
    </view>

    <!-- 经历描述 -->
    <view class="formItem">
      <text class="label">经历描述</text>
      <textarea class="contentInput"
                placeholder="请输入详细内容描述。"
                value="{{schoolEditFormData.content}}"
                data-field="content"
                bindinput="handleInput"
                maxlength="-1"
                auto-height>
      </textarea>
    </view>
  </view>

  <!-- 底部按钮 -->
  <view class="buttonGroup">
    <button class="saveBtn" bindtap="saveSchool">保存信息</button>
    <button wx:if="{{editIndex >= 0}}" class="deleteBtn" bindtap="deleteSchool">删除</button>
  </view>
</view>