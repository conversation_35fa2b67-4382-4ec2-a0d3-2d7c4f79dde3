/* 错误上报死循环修复测试样式 */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
  padding: 30rpx;
  background-color: #fff;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
}

.status-section {
  margin-bottom: 30rpx;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.status-item {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.status-item:last-child {
  margin-bottom: 0;
}

.status-label {
  font-size: 30rpx;
  color: #333;
  width: 150rpx;
}

.status-value {
  font-size: 30rpx;
  font-weight: bold;
}

.status-value.connected {
  color: #34c759;
}

.status-value.disconnected {
  color: #ff3b30;
}

.status-value.unknown {
  color: #999;
}

.status-value.ready {
  color: #007aff;
}

.status-value.running {
  color: #ff9500;
}

.status-value.completed {
  color: #34c759;
}

.controls {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.manual-controls {
  margin-bottom: 30rpx;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.manual-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.btn {
  height: 80rpx;
  border-radius: 10rpx;
  font-size: 30rpx;
  border: none;
}

.btn.primary {
  flex: 1;
  background-color: #007aff;
  color: #fff;
}

.btn.primary[disabled] {
  background-color: #ccc;
  color: #999;
}

.btn.secondary {
  flex: 1;
  background-color: #fff;
  color: #007aff;
  border: 2rpx solid #007aff;
}

.btn.secondary[disabled] {
  background-color: #f5f5f5;
  color: #ccc;
  border-color: #ccc;
}

.btn.small {
  padding: 0 20rpx;
  height: 60rpx;
  font-size: 26rpx;
  background-color: #f8f8f8;
  color: #333;
  border: 1rpx solid #ddd;
}

.results-container {
  background-color: #fff;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
  overflow: hidden;
  margin-bottom: 30rpx;
}

.results-header {
  padding: 30rpx;
  background-color: #f8f8f8;
  border-bottom: 1rpx solid #eee;
}

.results-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.results-list {
  max-height: 600rpx;
}

.result-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.result-item:last-child {
  border-bottom: none;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10rpx;
}

.result-message {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
}

.result-time {
  font-size: 24rpx;
  color: #999;
  margin-left: 20rpx;
}

.result-data {
  margin-top: 15rpx;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
}

.data-content {
  font-size: 24rpx;
  color: #666;
  font-family: monospace;
  word-break: break-all;
}

.empty-state {
  padding: 60rpx;
  text-align: center;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.tips {
  padding: 20rpx;
  background-color: #fff3cd;
  border-radius: 10rpx;
  border-left: 4rpx solid #ffc107;
}

.tips-title {
  display: block;
  font-size: 30rpx;
  font-weight: bold;
  color: #856404;
  margin-bottom: 15rpx;
}

.tips-text {
  display: block;
  font-size: 26rpx;
  color: #856404;
  line-height: 1.5;
  margin-bottom: 10rpx;
}

.tips-text:last-child {
  margin-bottom: 0;
}
