<!--错误上报死循环修复测试-->
<view class="container">
  <view class="header">
    <text class="title">错误上报死循环修复测试</text>
    <text class="subtitle">验证网络断开时不再出现死循环</text>
  </view>

  <view class="status-section">
    <view class="status-item">
      <text class="status-label">网络状态:</text>
      <text class="status-value {{networkStatus}}">
        {{networkStatus === 'connected' ? '已连接' : networkStatus === 'disconnected' ? '已断开' : '未知'}}
      </text>
    </view>
    <view class="status-item">
      <text class="status-label">测试状态:</text>
      <text class="status-value {{testStatus}}">
        {{testStatus === 'ready' ? '准备就绪' : testStatus === 'running' ? '运行中...' : '已完成'}}
      </text>
    </view>
  </view>

  <view class="controls">
    <button class="btn primary" bindtap="runTests" disabled="{{testStatus === 'running'}}">
      运行测试
    </button>
    <button class="btn secondary" bindtap="clearResults" disabled="{{testStatus === 'running'}}">
      清除结果
    </button>
  </view>

  <view class="manual-controls">
    <text class="section-title">手动测试</text>
    <view class="manual-buttons">
      <button class="btn small" bindtap="triggerNetworkError">触发网络错误</button>
      <button class="btn small" bindtap="checkErrorReporterStatus">检查上报器状态</button>
      <button class="btn small" bindtap="checkNetworkStatus">检查网络状态</button>
    </view>
  </view>

  <view class="results-container">
    <view class="results-header">
      <text class="results-title">测试结果 ({{testResults.length}})</text>
    </view>
    
    <scroll-view class="results-list" scroll-y="true">
      <view class="result-item" wx:for="{{testResults}}" wx:key="index">
        <view class="result-header">
          <text class="result-message">{{item.message}}</text>
          <text class="result-time">{{item.timestamp}}</text>
        </view>
        <view class="result-data" wx:if="{{item.data}}">
          <text class="data-content">{{item.data}}</text>
        </view>
      </view>
      
      <view class="empty-state" wx:if="{{testResults.length === 0}}">
        <text class="empty-text">暂无测试结果</text>
      </view>
    </scroll-view>
  </view>

  <view class="tips">
    <text class="tips-title">测试说明:</text>
    <text class="tips-text">1. 此测试验证简化后的错误上报系统</text>
    <text class="tips-text">2. 错误上报请求不重试，失败即丢弃</text>
    <text class="tips-text">3. 错误上报请求本身不会触发新的错误上报</text>
    <text class="tips-text">4. 可以手动断开网络后点击"触发网络错误"按钮测试</text>
    <text class="tips-text">5. 观察控制台日志，确认不会出现死循环</text>
  </view>
</view>
