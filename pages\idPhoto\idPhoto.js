// pages/idPhoto/idPhoto.js
const idPhotoAPI = require('../../utils/api/idPhotoAPI');

Page({
  data: {
    selectedSize: '', // 选中的尺寸
    selectedImage: '', // 选中的图片路径
    processing: false, // 是否正在处理
    loading: true, // 页面加载状态
    sizeOptions: [], // 从服务器获取的尺寸选项
    colorOptions: [] // 从服务器获取的颜色选项
  },

  onLoad(options) {

    this.loadInitialData();
  },

  /**
   * 加载初始数据
   */
  async loadInitialData() {
    try {
      this.setData({ loading: true });

      // 并行获取尺寸和颜色列表
      const [sizesResult, colorsResult] = await Promise.all([
        idPhotoAPI.getSizes(),
        idPhotoAPI.getColors()
      ]);



      if (sizesResult.success && colorsResult.success) {
        this.setData({
          sizeOptions: sizesResult.data.sizes || [],
          colorOptions: colorsResult.data.colors || [],
          loading: false
        });
      } else {
        throw new Error('获取数据失败');
      }

    } catch (error) {
      console.error('加载初始数据失败:', error);
      this.setData({ loading: false });

      // 简化错误处理，不显示弹窗

    }
  },

  /**
   * 选择尺寸
   */
  selectSize(e) {
    const size = e.currentTarget.dataset.size;
    console.log('选择尺寸:', size);

    this.setData({
      selectedSize: size
    });
  },

  /**
   * 选择图片
   */
  chooseImage() {
    const that = this;

    // 添加触觉反馈
    // wx.vibrateShort({
    //   type: 'light'
    // });

    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      maxDuration: 30,
      camera: 'back',
      success(res) {
        console.log('选择图片成功:', res);
        const tempFilePath = res.tempFiles[0].tempFilePath;
        that.setData({
          selectedImage: tempFilePath
        });

      },
      fail(err) {
        console.warn('选择图片失败,或取消选择:', err);
      }
    });
  },

  /**
   * 开始处理证件照
   */
  startProcess() {
    if (!this.data.selectedSize || !this.data.selectedImage) {
      wx.showToast({
        title: '请选择尺寸和照片',
        icon: 'error'
      });
      return;
    }

    console.log('开始处理证件照');
    console.log('选中尺寸:', this.data.selectedSize);
    console.log('选中图片:', this.data.selectedImage);

    // 添加触觉反馈
    // wx.vibrateShort({
    //   type: 'medium'
    // });

    // 显示加载提示
    wx.showLoading({
      title: '正在跳转...',
      mask: true
    });

    // 获取选中尺寸的详细信息
    const selectedSizeInfo = this.data.sizeOptions.find(item => item.value === this.data.selectedSize);

    // 跳转到处理页面，传递参数
    wx.navigateTo({
      url: `/pages/idPhoto/result/result?image=${encodeURIComponent(this.data.selectedImage)}&size=${this.data.selectedSize}&sizeName=${encodeURIComponent(selectedSizeInfo?.name || '')}`,
      success: () => {
        wx.hideLoading();
      },
      fail: () => {
        wx.hideLoading();
        wx.showToast({
          title: '跳转失败',
          icon: 'error'
        });
      }
    });
  }
});
