const app = getApp();

Page({
  data: {
    modules: [
      { id: 1, type: 'basicInfo', name: '基本信息'},
      { id: 2, type: 'jobIntention', name: '求职意向'},
      { id: 3, type: 'education', name: '教育经历'},
      { id: 4, type: 'school', name: '在校经历'},
      { id: 5, type: 'internship', name: '实习经历'},
      { id: 6, type: 'work', name: '工作经历'},
      { id: 7, type: 'project', name: '项目经历'},
      { id: 8, type: 'skills', name: '技能特长'},
      { id: 9, type: 'awards', name: '奖项证书'},
      { id: 10, type: 'interests', name: '兴趣爱好'},
      { id: 11, type: 'evaluation', name: '自我评价'},
      { id: 12, type: 'custom1', name: '定制模块'},
      { id: 13, type: 'custom2', name: '定制模块'},
      { id: 14, type: 'custom3', name: '定制模块'}
    ],
    activeModules: [], // 存储已填写数据的模块 (包含所有有数据的模块)
    moduleOrders: {}
  },

  onLoad: function() {
    console.log('========== 模块管理页面 onLoad ==========');
    // 从全局简历管理器加载数据
    this.loadActiveModules();
  },

  // 加载已填写数据的模块
  loadActiveModules: function() {
    console.log('========== 开始加载模块数据 ==========');

    try {
      // 通过全局简历管理器获取当前简历数据
      const resumeManager = app.getResumeManager();
      const currentResume = resumeManager.getCurrentResume();

      if (!currentResume) {
        console.error('无法获取当前简历数据');
        return;
      }

      console.log('从全局简历管理器获取的数据:', currentResume);

      // 首先更新自定义模块名称
      this.updateCustomModuleNames(currentResume);

      // 获取模块顺序
      let moduleOrders = currentResume.moduleOrders || {};
      console.log('当前模块顺序:', moduleOrders);

      // 检查各模块是否有数据，创建活动模块列表
      const filledModules = [];

      // 检查基本信息
      if (currentResume.basicInfo && currentResume.basicInfo.name) {
        filledModules.push('basicInfo');
      }

      // 检查求职意向
      const jobIntention = currentResume.jobIntention;
      if (jobIntention && (jobIntention.position || jobIntention.location || jobIntention.salary || jobIntention.status)) {
        filledModules.push('jobIntention');
      }

      // 检查教育经历
      if (currentResume.education && currentResume.education.length > 0) {
        filledModules.push('education');
      }

      // 检查在校经历
      if (currentResume.school && currentResume.school.length > 0) {
        filledModules.push('school');
      }

      // 检查实习经历
      if (currentResume.internship && currentResume.internship.length > 0) {
        filledModules.push('internship');
      }

      // 检查工作经历
      if (currentResume.work && currentResume.work.length > 0) {
        filledModules.push('work');
      }

      // 检查项目经历
      if (currentResume.project && currentResume.project.length > 0) {
        filledModules.push('project');
      }

      // 检查技能特长
      if (currentResume.skills && currentResume.skills.toArray().length > 0) {
        filledModules.push('skills');
      }

      // 检查奖项证书
      if (currentResume.awards && currentResume.awards.toArray().length > 0) {
        filledModules.push('awards');
      }

      // 检查兴趣爱好
      if (currentResume.interests && currentResume.interests.toArray().length > 0) {
        filledModules.push('interests');
      }

      // 检查自我评价
      if (currentResume.evaluation && currentResume.evaluation.length > 0) {
        filledModules.push('evaluation');
      }

      // 检查自定义模块
      if (currentResume.custom1 && currentResume.custom1.length > 0 &&
          currentResume.custom1.some(item => item.customName || item.content || item.role)) {
        filledModules.push('custom1');
      }
      if (currentResume.custom2 && currentResume.custom2.length > 0 &&
          currentResume.custom2.some(item => item.customName || item.content || item.role)) {
        filledModules.push('custom2');
      }
      if (currentResume.custom3 && currentResume.custom3.length > 0 &&
          currentResume.custom3.some(item => item.customName || item.content || item.role)) {
        filledModules.push('custom3');
      }

      console.log('检测到有数据的模块:', filledModules);

      // 补全模块顺序（确保所有模块都有顺序值）
      moduleOrders = this.completeFillModuleOrders(moduleOrders);

      // 过滤出有数据的模块
      let activeModules = this.data.modules.filter(module =>
        filledModules.includes(module.type)
      );

      // 按照模块顺序排序
      activeModules = activeModules.sort((a, b) => {
        const orderA = moduleOrders[a.type] !== undefined ? moduleOrders[a.type] : Infinity;
        const orderB = moduleOrders[b.type] !== undefined ? moduleOrders[b.type] : Infinity;
        return orderA - orderB;
      });

      console.log('排序后的活动模块:', activeModules.map(m => `${m.name}(${moduleOrders[m.type]})`));

      // 更新数据
      this.setData({
        activeModules: activeModules,
        moduleOrders: moduleOrders
      });

      console.log('========== 模块数据加载完成 ==========');
    } catch (error) {
      console.error('加载模块数据失败:', error);
    }
  },

  // 补全模块顺序
  completeFillModuleOrders(moduleOrders) {
    console.log('========== 补全模块顺序 ==========');
    console.log('补全前的moduleOrders:', moduleOrders);

    // 找到当前最大的顺序值
    let maxOrder = 0;
    this.data.modules.forEach(module => {
      if (moduleOrders[module.type] !== undefined) {
        maxOrder = Math.max(maxOrder, moduleOrders[module.type]);
      }
    });

    console.log('当前最大顺序值:', maxOrder);

    // 为没有顺序的模块分配顺序值
    this.data.modules.forEach(module => {
      const type = module.type;
      if (moduleOrders[type] === undefined) {
        maxOrder++;
        moduleOrders[type] = maxOrder;
      }
    });

    console.log('补全后的moduleOrders:', moduleOrders);
    return moduleOrders;
  },

  // 更新自定义模块名称
  updateCustomModuleNames: function(currentResume) {
    console.log('========== 更新自定义模块名称 ==========');

    const modules = [...this.data.modules]; // 创建副本

    // 更新custom1模块名称
    if (currentResume.custom1 && currentResume.custom1.length > 0) {
      const custom1Data = currentResume.custom1[0];
      if (custom1Data.customName && custom1Data.customName.trim()) {
        const custom1Module = modules.find(m => m.type === 'custom1');
        if (custom1Module) {
          custom1Module.name = custom1Data.customName;
          console.log('更新custom1模块名称:', custom1Data.customName);
        }
      }
    }

    // 更新custom2模块名称
    if (currentResume.custom2 && currentResume.custom2.length > 0) {
      const custom2Data = currentResume.custom2[0];
      if (custom2Data.customName && custom2Data.customName.trim()) {
        const custom2Module = modules.find(m => m.type === 'custom2');
        if (custom2Module) {
          custom2Module.name = custom2Data.customName;
          console.log('更新custom2模块名称:', custom2Data.customName);
        }
      }
    }

    // 更新custom3模块名称
    if (currentResume.custom3 && currentResume.custom3.length > 0) {
      const custom3Data = currentResume.custom3[0];
      if (custom3Data.customName && custom3Data.customName.trim()) {
        const custom3Module = modules.find(m => m.type === 'custom3');
        if (custom3Module) {
          custom3Module.name = custom3Data.customName;
          console.log('更新custom3模块名称:', custom3Data.customName);
        }
      }
    }

    // 更新data中的modules
    this.setData({
      modules: modules
    });

    console.log('========== 自定义模块名称更新完成 ==========');
  },
  // 向上移动模块
  moveUp: function(e) {
    const index = e.currentTarget.dataset.index;
    if (index <= 0) return; // 已经是第一个，无法上移

    const activeModules = [...this.data.activeModules];
    const currentModule = activeModules[index];
    const targetModule = activeModules[index - 1];

    // 检查是否涉及基本信息模块
    if (currentModule.type === 'basicInfo' || targetModule.type === 'basicInfo') {
      wx.showToast({ title: '基本信息模块不能移动', icon: 'none' });
      return;
    }

    console.log(`向上移动模块: ${currentModule.name}，当前位置: ${index}`);

    // 交换当前项目和上一个项目的位置
    [activeModules[index], activeModules[index - 1]] = [activeModules[index - 1], activeModules[index]];

    this.setData({
      activeModules: activeModules
    });

    // 添加视觉反馈
    // wx.vibrateShort();
  },

  // 向下移动模块
  moveDown: function(e) {
    const index = e.currentTarget.dataset.index;
    const maxIndex = this.data.activeModules.length - 1;
    if (index >= maxIndex) return; // 已经是最后一个，无法下移

    const activeModules = [...this.data.activeModules];
    const currentModule = activeModules[index];
    const targetModule = activeModules[index + 1];

    // 检查是否涉及基本信息模块
    if (currentModule.type === 'basicInfo' || targetModule.type === 'basicInfo') {
      wx.showToast({ title: '基本信息模块不能移动', icon: 'none' });
      return;
    }

    console.log(`向下移动模块: ${currentModule.name}，当前位置: ${index}`);

    // 交换当前项目和下一个项目的位置
    [activeModules[index], activeModules[index + 1]] = [activeModules[index + 1], activeModules[index]];

    this.setData({
      activeModules: activeModules
    });

    // 添加视觉反馈
    // wx.vibrateShort();
  },





  // 保存模块设置
  saveModuleSettings: function() {
    console.log('========== 开始保存模块设置 ==========');

    try {
      const activeModules = [...this.data.activeModules]; // 创建副本避免修改原数据
      console.log('当前排序后的模块:', activeModules.map(m => m.name));

      // 创建新的模块顺序对象
      const newModuleOrders = {};

      // 检查是否有 basicInfo 模块
      const basicInfoIndex = activeModules.findIndex(module => module.type === 'basicInfo');

      if (basicInfoIndex !== -1) {
        // 如果有 basicInfo，先移除它
        const basicInfoModule = activeModules.splice(basicInfoIndex, 1)[0];
        console.log('找到 basicInfo 模块，将其移到第一位');

        // basicInfo 始终排在第一位（order = 0）
        newModuleOrders['basicInfo'] = 0;

        // 其他模块从 1 开始排序
        activeModules.forEach((module, index) => {
          newModuleOrders[module.type] = index + 1;
        });

        // 重新构建完整的 activeModules 列表（用于显示）
        activeModules.unshift(basicInfoModule);
      } else {
        // 如果没有 basicInfo，直接从 0 开始排序
        activeModules.forEach((module, index) => {
          newModuleOrders[module.type] = index;
        });
      }

      console.log('新的模块顺序:', newModuleOrders);

      // 通过全局简历管理器保存数据
      const resumeManager = app.getResumeManager();
      const currentResume = resumeManager.getCurrentResume();

      if (currentResume) {
        // 更新简历实例中的模块顺序
        currentResume.moduleOrders = newModuleOrders;

        // 保存到存储
        resumeManager.saveToStorage();

        console.log('模块顺序已保存到全局简历管理器');
      } else {
        console.error('无法获取当前简历实例');
        wx.showToast({ title: '保存失败', icon: 'none' });
        return;
      }

      // 兼容性：同时保存到旧的存储方式
      wx.setStorageSync('activeModules', activeModules);
      wx.setStorageSync('moduleOrders', newModuleOrders);

      console.log('========== 模块设置保存完成 ==========');

      // 显示成功提示并返回
      wx.showToast({ title: '保存成功', icon: 'success', duration: 500 });
      setTimeout(() => { wx.navigateBack(); }, 500);

    } catch (error) {
      console.error('保存模块设置失败:', error);
      wx.showToast({ title: '保存失败', icon: 'none' });
    }
  }
});