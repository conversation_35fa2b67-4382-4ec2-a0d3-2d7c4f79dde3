const ResumeFormHelper = require('../../../../utils/resume/ResumeFormHelper.js');
const app = getApp();

Page({
  data: {
    custom2FormData: null, // 将在 onLoad 中初始化
    currentDate: '' // 当前日期，用于"至今"后重新选择日期时的默认值
  },

  onLoad(options) {
    console.log('=== 自定义模块2页面加载 ===');

    // 初始化当前日期（格式：YYYY-MM）
    const now = new Date();
    const currentDate = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
    this.setData({
      currentDate: currentDate
    });

    this.loadCustom2Data();
  },

  /**
   * 加载自定义模块2数据
   */
  loadCustom2Data() {
    try {
      const custom2Data = ResumeFormHelper.loadFieldData('custom2', app);

      // 自定义模块是数组，取第一个元素，如果没有则使用空数据
      let formData;
      if (Array.isArray(custom2Data) && custom2Data.length > 0) {
        formData = custom2Data[0];
        console.log('✅ 加载自定义模块2数据成功:', formData);
      } else {
        // 使用空的 CustomItem 实例
        formData = ResumeFormHelper.getEmptyFieldData('customItem');
        console.log('📝 创建空的自定义模块2数据:', formData);
      }

      this.setData({
        custom2FormData: {
          index: '2',
          startDate: formData.startDate || '',
          endDate: formData.endDate || '',
          customName: formData.customName || '',
          role: formData.role || '',
          content: formData.content || ''
        }
      });

    } catch (error) {
      console.error('❌ 加载自定义模块2数据失败:', error);
      // 出错时使用空数据
      this.setData({
        custom2FormData: {
          index: '2',
          startDate: '',
          endDate: '',
          customName: '',
          role: '',
          content: ''
        }
      });
    }
  },

  // 处理开始日期变化
  handleStartDateChange(e) {
    this.setData({
      'custom2FormData.startDate': e.detail.value
    });
  },

  // 处理结束日期变化
  handleEndDateChange(e) {
    this.setData({
      'custom2FormData.endDate': e.detail.value
    });
  },

  // 设置"至今"
  setToNow() {
    this.setData({
      'custom2FormData.endDate': '至今'
    });
  },

  // 处理名称输入
  handleNameInput(e) {
    this.setData({
      'custom2FormData.customName': e.detail.value
    });
  },

  // 处理角色输入
  handleRoleInput(e) {
    this.setData({
      'custom2FormData.role': e.detail.value
    });
  },

  // 处理内容输入
  handleContentInput(e) {
    this.setData({
      'custom2FormData.content': e.detail.value
    });
  },



  // 保存内容
  saveContent() {
    const { custom2FormData } = this.data;
    const {
      startDate,
      endDate,
      customName,
      role,
      content
    } = custom2FormData;

    // 添加必填校验
    if (!customName || customName.trim() === '') {
      wx.showToast({
        title: '请输入名称',
        icon: 'none'
      });
      return;
    }

    const saveData = {
      startDate,
      endDate,
      customName,
      role,
      content
    };

    // 使用 ResumeFormHelper 统一保存
    const success = ResumeFormHelper.saveFieldData('custom2', [saveData], app);

    if (!success) {
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
      return;
    }

    wx.showToast({
      title: '保存成功',
      icon: 'success',
      duration: 1000,
      success: () => {
        setTimeout(() => {
          wx.navigateBack();
        }, 500);
      }
    });
  },

  // 删除内容
  deleteContent() {
    wx.showModal({
      title: '提示',
      content: '确定要删除内容吗？',
      success: (res) => {
        if (res.confirm) {
          // 使用 ResumeFormHelper 清空数据
          const success = ResumeFormHelper.clearFieldData('custom2', app);

          if (success) {
            this.setData({
              custom2FormData: {
                ...this.data.custom2FormData,
                startDate: '',
                endDate: '',
                customName: '',
                role: '',
                content: ''
              }
            });
          } else {
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            });
            return;
          }

          wx.showToast({
            title: '已删除',
            icon: 'success',
            duration: 2000,
            success: () => {
              setTimeout(() => {
                wx.navigateBack();
              }, 500);
            }
          });
        }
      }
    });
  }
});