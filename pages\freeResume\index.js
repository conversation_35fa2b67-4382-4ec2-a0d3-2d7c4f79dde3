// 免费模板页面
const freeTemplateApi = require('../../utils/api/freeTemplateApi');
const app = getApp();

Page({
  data: {
    templates: [],           // 模板列表
    isLoading: false,        // 是否正在加载
    isRefreshing: false,     // 是否正在刷新
    isLoadingMore: false,    // 是否正在加载更多
    hasMore: true,           // 是否还有更多数据
    skip: 0,                 // 跳过的记录数
    limit: 20,               // 每次加载数量

    // 下载浮窗相关
    showDownloadModal: false,    // 是否显示下载浮窗
    selectedTemplate: null,      // 选中的模板
    downloadData: null,          // 下载数据
    isLoadingDownload: false,    // 是否正在加载下载链接
  },

  onLoad() {
    this.loadTemplates();
  },

  onShow() {
    // 页面显示时刷新数据
    if (this.data.templates.length === 0) {
      this.loadTemplates();
    }
  },

  /**
   * 加载模板列表
   */
  async loadTemplates(isRefresh = false) {
    if (this.data.isLoading && !isRefresh) return;

    try {
      this.setData({
        isLoading: !isRefresh,
        isRefreshing: isRefresh
      });

      const skip = isRefresh ? 0 : this.data.skip;
      const response = await freeTemplateApi.getFreeTemplateList({
        skip: skip,
        limit: this.data.limit,
        type: 'word' // 指定获取Word模板
      });

      if (response && response.templates) {
        const newTemplates = (response.templates || []).map((template, index) => {
          // 兼容处理：确保必要字段存在
          const processedTemplate = {
            id: template.id || '',
            thumb_url: template.thumb_url || '',
            baidu_url: template.baidu_url || '',
            baidu_pass: template.baidu_pass || '',
            quark_url: template.quark_url || '',
            quark_pass: template.quark_pass || '',
            type: template.type || 'word',
            // 添加UI状态字段
            isLoading: true,  // 初始化时设置为加载状态
            imageError: false, // 初始化错误状态
            // 添加唯一标识符，确保wx:key不重复
            uniqueKey: `${template.id || 'template'}_${Date.now()}_${index}_${Math.random().toString(36).substring(2, 11)}`
          };



          return processedTemplate;
        });

        // 去重处理：基于id字段去重，避免重复数据
        let finalTemplates;
        if (isRefresh) {
          finalTemplates = newTemplates;
        } else {
          const existingIds = new Set(this.data.templates.map(t => t.id));
          const uniqueNewTemplates = newTemplates.filter(template => !existingIds.has(template.id));
          finalTemplates = [...this.data.templates, ...uniqueNewTemplates];


        }

        const total = response.total || 0;
        const currentTotal = finalTemplates.length;
        const hasMore = currentTotal < total;

        this.setData({
          templates: finalTemplates,
          hasMore: hasMore,
          skip: isRefresh ? newTemplates.length : this.data.skip + (newTemplates.length - (newTemplates.length - (finalTemplates.length - this.data.templates.length))),
          isLoading: false,
          isRefreshing: false
        });

        // 如果是刷新，显示成功提示
        if (isRefresh && newTemplates.length > 0) {
          wx.showToast({
            title: '刷新成功',
            icon: 'none',
            duration: 500
          });
        }
      } else {
        throw new Error('获取模板列表失败');
      }
    } catch (error) {
      console.error('加载模板列表失败:', error);

      // 上报免费模板获取失败错误
      app.reportError('free_template_fetch_error', error, {
        page: 'freeResume/index',
        action: 'loadTemplates',
        is_refresh: isRefresh,
        skip: this.data.skip,
        limit: this.data.limit,
        has_response: !!error.response,
        status_code: error.response?.status
      });

      let errorMessage = '网络连接失败，请检查网络';

      // 根据错误类型提供更具体的错误信息
      if (error.message && error.message.includes('500')) {
        errorMessage = '服务器内部错误，请联系管理员';
      } else if (error.message && error.message.includes('timeout')) {
        errorMessage = '请求超时，请检查网络';
      } else if (error.message && error.message.includes('网络')) {
        errorMessage = '网络连接失败，请检查网络';
      }

      this.setData({
        isLoading: false,
        isRefreshing: false
      });

      // 简化错误处理，只使用微信官方Toast提示
      wx.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 1500
      });
    }
  },

  /**
   * 加载更多模板
   */
  async loadMoreTemplates() {
    if (this.data.isLoadingMore || !this.data.hasMore) return;

    try {
      this.setData({
        isLoadingMore: true
      });

      const response = await freeTemplateApi.getFreeTemplateList({
        skip: this.data.skip,
        limit: this.data.limit,
        type: 'word'
      });

      if (response && response.templates) {
        const newTemplates = (response.templates || []).map((template, index) => {
          // 兼容处理：确保必要字段存在
          const processedTemplate = {
            id: template.id || '',
            thumb_url: template.thumb_url || '',
            baidu_url: template.baidu_url || '',
            baidu_pass: template.baidu_pass || '',
            quark_url: template.quark_url || '',
            quark_pass: template.quark_pass || '',
            type: template.type || 'word',
            // 添加UI状态字段
            isLoading: true,  // 初始化时设置为加载状态
            imageError: false, // 初始化错误状态
            // 添加唯一标识符，确保wx:key不重复
            uniqueKey: `${template.id || 'template'}_${Date.now()}_${index}_${Math.random().toString(36).substring(2, 11)}`
          };



          return processedTemplate;
        });

        // 去重处理：基于id字段去重，避免重复数据
        const existingIds = new Set(this.data.templates.map(t => t.id));
        const uniqueNewTemplates = newTemplates.filter(template => !existingIds.has(template.id));



        const finalTemplates = [...this.data.templates, ...uniqueNewTemplates];
        const total = response.total || 0;
        const hasMore = finalTemplates.length < total;

        this.setData({
          templates: finalTemplates,
          hasMore: hasMore,
          skip: this.data.skip + uniqueNewTemplates.length,
          isLoadingMore: false
        });
      }
    } catch (error) {
      console.error('加载更多失败:', error);
      this.setData({
        isLoadingMore: false
      });

      // 简化错误处理，只使用微信官方Toast提示
      wx.showToast({
        title: '加载更多失败',
        icon: 'none',
        duration: 2000
      });
    }
  },

  /**
   * 下拉刷新
   */
  onRefresh() {
    this.loadTemplates(true);
  },

  /**
   * 上拉加载更多
   */
  onLoadMore() {
    this.loadMoreTemplates();
  },

  /**
   * 重试加载
   */
  onRetry() {
    this.setData({
      templates: [],
      skip: 0,
      hasMore: true
    });
    this.loadTemplates();
  },

  /**
   * 图片加载成功
   */
  onImageLoad(e) {
    const index = e.currentTarget.dataset.index;
    const templates = this.data.templates;
    if (templates[index]) {
      templates[index].isLoading = false;
      this.setData({
        templates: templates
      });
    }
  },

  /**
   * 图片加载失败
   */
  onImageError(e) {
    const index = e.currentTarget.dataset.index;
    const templates = this.data.templates;
    if (templates[index]) {
      const template = templates[index];
      template.isLoading = false;
      template.imageError = true; // 标记图片加载失败

      // 使用base64编码的占位符图片，避免路径问题
      template.thumb_url = 'data:image/svg+xml;base64,' + btoa(`
        <svg width="280" height="400" xmlns="http://www.w3.org/2000/svg">
          <rect width="280" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>
          <g transform="translate(140, 150)">
            <rect x="-30" y="-40" width="50" height="70" fill="#6c757d" rx="3"/>
            <polygon points="20,-40 20,-25 35,-25" fill="#495057"/>
            <line x1="-20" y1="-25" x2="10" y2="-25" stroke="white" stroke-width="2"/>
            <line x1="-20" y1="-15" x2="15" y2="-15" stroke="white" stroke-width="2"/>
            <line x1="-20" y1="-5" x2="15" y2="-5" stroke="white" stroke-width="2"/>
            <line x1="-20" y1="5" x2="10" y2="5" stroke="white" stroke-width="2"/>
          </g>
          <text x="140" y="250" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="#6c757d">暂无图片</text>
        </svg>
      `);

      this.setData({
        templates: templates
      });
    }
  },

  /**
   * 点击模板
   */
  onTemplateClick(e) {
    const template = e.currentTarget.dataset.template;

    if (!template || !template.id) {
      wx.showToast({
        title: '模板信息无效',
        icon: 'none'
      });
      return;
    }

    // 检查模板是否包含下载链接
    if (!template.baidu_url && !template.quark_url) {
      // 上报模板下载链接缺失错误
      app.reportError('free_template_no_download_link', '模板缺少下载链接', {
        page: 'freeResume/index',
        action: 'onTemplateClick',
        template_id: template.id,
        template_name: template.name
      });

      wx.showToast({
        title: '暂无下载链接',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 直接使用模板数据中的下载链接信息
    const downloadData = {
      baidu_url: template.baidu_url,
      baidu_pass: template.baidu_pass,
      quark_url: template.quark_url,
      quark_pass: template.quark_pass
    };

    // 设置选中的模板并显示浮窗
    this.setData({
      selectedTemplate: template,
      showDownloadModal: true,
      downloadData: downloadData,
      isLoadingDownload: false
    });
  },

  /**
   * 关闭下载浮窗
   */
  onCloseDownloadModal() {
    this.setData({
      showDownloadModal: false,
      selectedTemplate: null,
      downloadData: null,
      isLoadingDownload: false
    });
  },

  /**
   * 复制成功回调
   */
  onCopySuccess() {
    // 可以在这里添加统计逻辑
  },

  /**
   * 复制失败回调
   */
  onCopyFail(e) {
    const { type, error } = e.detail;
    console.error('复制失败:', type, error);
  },





  /**
   * 页面分享
   */
  onShareAppMessage() {
    // 如果有动态设置的分享数据，使用它
    if (this.shareData) {
      const data = this.shareData;
      this.shareData = null; // 清除临时数据
      return data;
    }

    // 如果当前有选中的模板，使用模板信息进行分享
    if (this.data.selectedTemplate) {
      return {
        title: '个人简历模板 - 免费模板下载',
        path: '/pages/freeResume/index',
        imageUrl: this.data.selectedTemplate.thumb_url || './images/share-cover.png'
      };
    }

    // 默认分享数据
    return {
      title: '免费简历模板下载',
      path: '/pages/freeResume/index',
      imageUrl: './images/share-cover.png'
    };
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    return {
      title: '免费简历模板下载 - 精美Word模板',
      imageUrl: './images/share-cover.png'
    };
  }
});
