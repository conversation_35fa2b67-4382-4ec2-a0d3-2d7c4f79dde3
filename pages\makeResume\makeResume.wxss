.container {
  width: 100%;
  min-height: 100vh;
  background-color: #4B8BF5;
  padding-bottom: 180rpx;
}

.content {
  width: 100%;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  overflow-y: auto;
}

.welcomeSection {
  color: #fff;
  margin-bottom: 30rpx;
}

.welcomeTitle {
  font-size: 36rpx;
  margin-bottom: 20rpx;
}

.welcomeDesc {
  font-size: 28rpx;
  line-height: 1.5;
  opacity: 0.9;
}

.infoCard {
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  padding: 0 30rpx;
  margin-bottom: 30rpx;
}

.infoSection {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.infoSection .sectionTitle {
  margin-top: 10rpx;
  margin-bottom: 10rpx;
  font-size: 32rpx;
  color: #333;
  font-weight: 700;
  text-align: left;
  position: relative;
  padding-left: 20rpx;
}

.infoSection .sectionTitle::before {
  content: '';
  display: block;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx;
  background-color: #4B8BF5;
  border-radius: 4rpx;
}

.infoSection:first-child .sectionTitle {
  margin-top: 0;
}

.sectionTitle {
  font-size: 32rpx;
  color: #333;
  font-weight: 700;
  margin-bottom: 16rpx;
  text-align: left;
}

.sectionContent {
  display: block;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
  min-height: 60rpx;
}

/* 基本信息的特殊布局 */
.infoSection:first-child .sectionContent {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.sectionContent:last-child {
  border-bottom: none;
}

.mainInfo {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.name {
  font-size: 34rpx;
  color: #333;
  font-weight: 500;
}

.name.placeholder {
  color: #999;
  font-style: italic;
}

.school {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.subText {
  font-size: 28rpx;
  color: #666;
}

.dateText {
  font-size: 26rpx;
  color: #999;
  min-width: 180rpx;
  text-align: right;
  position: relative;  /* 添加相对定位 */
  top: 25rpx;        /* 可以通过调整这个值来上下移动，负值向上移动，正值向下移动 */
}

.addPhoto {
  width: 120rpx;
  height: 150rpx;
  border: 2rpx dashed #999;
  border-radius: 10rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.jobIntention,
.educationSection {
  padding: 30rpx 30rpx 0;
  border-top: 1rpx solid #f5f5f5;
}

.intentionTitle,
.educationTitle {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 700;
  position: relative;
  padding-left: 20rpx;
}

.intentionTitle::before,
.educationTitle::before {
  content: '';
  display: block;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx;
  background-color: #4B8BF5;
  border-radius: 4rpx;
}

.intentionContent,
.educationItem {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.intentionInfo {
  font-size: 28rpx;
  color: #666;
}

.intentionInfo text {
  margin-right: 16rpx;
}

.modulesSection {
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-top: 30rpx;
}

.modulesSection .sectionTitle {
  text-align: center;
  font-size: 32rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 30rpx;
  position: relative;
  padding-bottom: 15rpx;
}

.modulesSection .sectionTitle::after {
  content: '';
  display: block;
  position: absolute;
  left: 50%;
  bottom: 0;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background-color: #4B8BF5;
  border-radius: 3rpx;
}

.modulesGrid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.moduleItem {
  background: #f3f3f3;
  height: 100rpx;
  width: 85rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.moduleName {
  padding-left: 10rpx;
  font-size: 28rpx;
  color: #222;
  letter-spacing: 10rpx; /* 微调字距，提升均匀感 */

}

.addIcon {
  font-size: 40rpx;
  margin-bottom: 10rpx;
  color: #999;
}
.addText {
  height: 80rpx;
  width: 90rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bottomBar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx;
  background: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.actionButtons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20rpx;
  gap: 20rpx;
}

.actionBtn {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: linear-gradient(135deg, #4B8BF5, #3178F1);
  padding: 16rpx 24rpx;
  border-radius: 12rpx;
}

/* 普通按钮宽度25% */
.actionBtn:nth-child(1),
.actionBtn:nth-child(2) {
  width: 25%;
}

/* 生成简历按钮宽度40% */
.actionBtn:nth-child(3) {
  width: 40%;
}

.icon {
  width: 44rpx;
  height: 44rpx;
  margin-bottom: 8rpx;
}

.actionBtn text {
  font-size: 28rpx;
  color: #fff;
  white-space: nowrap;
  text-align: center;
}

.generateBtn {
  background: linear-gradient(135deg, #4B8BF5, #3178F1);
  color: #fff;
  padding: 20rpx 40rpx;
  border-radius: 40rpx;
  font-size: 32rpx;
}

.photo {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
  object-fit: cover;
  object-position: center;
}

/* 教育经历部分样式 */
.educationSection {
  background: #fff;
  margin: 20rpx;
  padding: 30rpx;
  border-radius: 12rpx;
}

.sectionTitle {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 20rpx;
}

.educationList {
  margin-top: 20rpx;
}

.educationItem {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.educationItem:last-child {
  border-bottom: none;
}

.schoolInfo {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.schoolName {
  font-size: 30rpx;
  color: #333;
}

.educationDate {
  font-size: 26rpx;
  color: #999;
}

.majorInfo {
  display: flex;
  align-items: center;
}

.degree {
  font-size: 28rpx;
  color: #666;
  margin-right: 20rpx;
}

.major {
  font-size: 28rpx;
  color: #666;
}

/* 求职意向样式 */
.jobInfo {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #666;
}

.jobInfo text {
  white-space: nowrap;  /* 防止文本换行 */
}

/* 移除之前的样式 */
.intentionContent,
.intentionInfo {
  display: block;
}

.intentionInfo text {
  margin-right: 0;
}

/* 如果需要特殊样式可以添加 */
.infoSection .title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.infoSection .subText {
  font-size: 28rpx;
  color: #666;
  margin-top: 8rpx;
}

.companyRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.companyName {
  font-size: 30rpx;
  color: #333;
  font-weight: normal;
}

.dateText {
  font-size: 26rpx;
  color: #999;
}

.positionText {
  font-size: 28rpx;
  color: #666;
}

.moduleSection {
  background: #fff;
  margin-top: 20rpx;
}

.moduleItem {
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.moduleTitle {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 20rpx;
}

.moduleContent {
  margin-top: 20rpx;
}

.experienceItem {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.experienceItem:last-child {
  margin-bottom: 0;
}

.companyInfo {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.company {
  font-size: 28rpx;
  color: #333;
}

.position {
  font-size: 26rpx;
  color: #666;
}

.date {
  font-size: 26rpx;
  color: #999;
  min-width: 180rpx;
  text-align: right;
}

.emptyTip {
  font-size: 28rpx;
  color: #999;
  text-align: center;
  padding: 20rpx 0;
}

.skillsList {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  padding: 10rpx 0;
}

.skillItem {
  background: #f5f7fa;
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.customModal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modalContent {
  width: 80%;
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.modalHeader {
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modalTitle {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.closeBtn {
  font-size: 40rpx;
  color: #999;
  padding: 0 20rpx;
}

.modalBody {
  padding: 30rpx;
}

.customInput {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #eee;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

/* 在校经历样式 */
.titleRow {
  position: relative;  /* 设置相对定位 */
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8rpx;
}

.date-container {
  position: relative;
  width: 180rpx;
  height: 1px;
}

.school-section .dateText {
  position: absolute;
  right: 0;
  top: 0;
  font-size: 26rpx;
  color: #999;
  min-width: 180rpx;
  text-align: right;
}

.title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.roleText {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.description {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-top: 8rpx;
  white-space: pre-wrap;
  word-break: break-all;
  /* 确保内容完整显示，不设置高度限制 */
}

/* 紧凑的基本信息样式 */
.compactInfo {
  margin-top: 10rpx;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.infoLine {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  word-break: break-all;
  display: block;
}

.placeholder {
  color: #bbb;
  font-style: italic;
}

/* 确保基本信息区域的布局 */
.infoSection:first-child .mainInfo {
  flex: 1;
  min-width: 0; /* 确保文本可以正确换行 */
}

/* 证件照操作浮窗样式 */
.photo-action-sheet {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.photo-action-sheet.show {
  opacity: 1;
  visibility: visible;
}

.action-sheet-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  padding-bottom: env(safe-area-inset-bottom);
}

.photo-action-sheet.show .action-sheet-content {
  transform: translateY(0);
}

.action-sheet-header {
  padding: 30rpx 40rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  text-align: center;
  position: relative;
}

.action-sheet-title {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.close-btn {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f5f5f5;
  transition: background-color 0.2s ease;
}

.close-btn:active {
  background: #e0e0e0;
}

.close-icon {
  font-size: 36rpx;
  color: #666;
  line-height: 1;
}

.action-sheet-body {
  padding: 0;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.action-item:active {
  background-color: #f5f5f5;
}

.action-item:last-child {
  border-bottom: none;
}

.action-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f8f9fa;
}

.action-icon.replace {
  background: #e3f2fd;
}

.action-icon.delete-icon {
  background: #ffebee;
}

.icon-text {
  font-size: 32rpx;
}

.action-text {
  font-size: 32rpx;
  color: #333;
  flex: 1;
}

.action-item.delete .action-text {
  color: #ff4757;
}

.action-sheet-footer {
  border-top: 16rpx solid #f5f5f5;
}

.action-item.cancel {
  border-bottom: none;
}

.action-item.cancel .action-text {
  color: #666;
  text-align: center;
  font-weight: 500;
}

/* 在校经历特定样式 */
.school-section .sectionContent {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 10rpx 0;
}

.school-section .mainInfo {
  flex: 1;
}

.school-section .dateText {
  position: absolute;
  right: 0;
  top: 20rpx;         /* 这里可以调整日期的上下位置 */
  font-size: 26rpx;
  color: #999;
  min-width: 180rpx;
  text-align: right;
}

.description-preview {
  max-height: 80rpx;  /* 设定固定高度 */
  overflow: hidden;
  line-height: 1.5;
  font-size: 28rpx;
}