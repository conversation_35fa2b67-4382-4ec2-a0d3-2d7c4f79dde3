// 服务器 URL 配置
const apiConfig = {
    // 全局超时配置
    timeout: {
        // 简历预览图片生成超时时间（毫秒）
        previewImage: 10000, // 10秒
        // PDF生成超时时间（毫秒）
        generatePDF: 10000,  // 10秒
        // 普通API请求超时时间（毫秒）
        default: 5000        // 5秒
    },
    dev: {
        // 基础URL
        // baseUrl: 'http://192.168.1.218:18080',
        // baseUrl:                  'http://192.168.1.218:18080',
        // idPhotoBaseUrl:           'http://192.168.1.218:18080',     // 证件照服务基础URL
        baseUrl:                  'https://resume.gbw8848.cn',
        idPhotoBaseUrl:           'https://resume.gbw8848.cn',     // 证件照服务基础URL

        // 简历相关API
        generatePDFUrl:           '/resumerender/export-pdf',
        exportJpegUrl:            '/resumerender/export-jpeg', // 新增JPEG预览图片API

        // 用户相关API
        loginUrl:                 '/auth/login', // 用户登录
        userInfoUrl:              '/auth/user', // 获取用户信息
        updateUserInfoUrl:        '/auth/user', // 更新用户信息

        // 反馈API
        feedbackUrl:              '/feedback/submit', // 提交用户反馈

        // 错误上报API
        errorReportUrl:           '/error/report', // 错误上报

        // 模板相关API
        freeTemplatesUrl:         '/free-templates',        // 获取模板列表


        // 简历样式相关API
        resumeStylesUrl:          '/free-templates/styles',    // 获取简历样式模板列表
        resumeStyleDetailUrl:     '/free-templates/styles',    // 获取简历样式模板详情

        // 证件照相关API (新版本)
        // idPhotoBaseUrl:           'http://hivisionidphotosapi.gbw8848.cn',     // 证件照服务基础URL
        idPhotoHealthUrl:         '/idphoto/health',           // 健康检查
        idPhotoGenerateUrl:       '/idphoto/generate',         // 生成证件照
        idPhotoSizesUrl:          '/idphoto/sizes',            // 获取支持的尺寸列表
        idPhotoColorsUrl:         '/idphoto/colors'            // 获取支持的颜色列表
    },
    
    prod: {
        // 基础URL
        baseUrl:                  'https://resume.gbw8848.cn',
        idPhotoBaseUrl:           'https://resume.gbw8848.cn',     // 证件照服务基础URL

        // 简历相关API
        generatePDFUrl:           '/resumerender/export-pdf',
        exportJpegUrl:            '/resumerender/export-jpeg', // 新增JPEG预览图片API

        // 用户相关API
        loginUrl:                 '/auth/login', // 用户登录
        userInfoUrl:              '/auth/user', // 获取用户信息
        updateUserInfoUrl:        '/auth/user', // 更新用户信息

        // 反馈API
        feedbackUrl:              '/feedback/submit', // 提交用户反馈

        // 错误上报API
        errorReportUrl:           '/error/report', // 错误上报

        // 模板相关API
        freeTemplatesUrl:         '/templates',        // 获取模板列表


        // 简历样式相关API
        resumeStylesUrl:          '/free-templates/styles',    // 获取简历样式模板列表
        resumeStyleDetailUrl:     '/free-templates/styles',    // 获取简历样式模板详情

        // 证件照相关API (新版本)
        // idPhotoBaseUrl:           'http://hivisionidphotosapi.gbw8848.cn',     // 证件照服务基础URL
        idPhotoHealthUrl:         '/idphoto/health',           // 健康检查
        idPhotoGenerateUrl:       '/idphoto/generate',         // 生成证件照
        idPhotoSizesUrl:          '/idphoto/sizes',            // 获取支持的尺寸列表
        idPhotoColorsUrl:         '/idphoto/colors'            // 获取支持的颜色列表
    }
};

// 根据环境导出配置
// 优先级：开发环境 > 测试环境 > 生产环境
function getEnvironment() {
  // 检查是否在开发者工具中
  const accountInfo = wx.getAccountInfoSync();
  if (accountInfo.miniProgram.envVersion === 'develop') {
    return 'dev';
  } else if (accountInfo.miniProgram.envVersion === 'trial') {
    return 'dev';
  } else {
    return 'prod';
  }
}

const env = getEnvironment();
console.log('当前API环境:', env, apiConfig[env]);

// 导出当前环境配置，同时包含全局超时配置
module.exports = {
  ...apiConfig[env],
  timeout: apiConfig.timeout
};