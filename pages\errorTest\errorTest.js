// pages/errorTest/errorTest.js
const app = getApp();

Page({
  data: {
    testResults: [],
    isRunning: false,
    errorReporterStatus: null
  },

  onLoad() {
    this.checkErrorReporterStatus();
  },

  /**
   * 检查错误上报器状态
   */
  checkErrorReporterStatus() {
    try {
      const errorReporter = require('../../utils/error/errorReporter');
      const status = errorReporter.getQueueStatus();
      
      this.setData({
        errorReporterStatus: {
          ...status,
          available: true
        }
      });
      
      console.log('错误上报器状态:', status);
    } catch (error) {
      console.error('获取错误上报器状态失败:', error);
      this.setData({
        errorReporterStatus: {
          available: false,
          error: error.message
        }
      });
    }
  },

  /**
   * 运行所有测试
   */
  async runAllTests() {
    if (this.data.isRunning) return;

    this.setData({
      isRunning: true,
      testResults: []
    });

    wx.showLoading({
      title: '运行测试中...',
      mask: true
    });

    const tests = [
      { name: '手动错误上报测试', method: 'testManualError' },
      { name: '网络错误模拟测试', method: 'testNetworkError' },
      { name: '业务逻辑错误测试', method: 'testBusinessError' },
      { name: '权限错误测试', method: 'testPermissionError' },
      { name: '数据错误测试', method: 'testDataError' },
      { name: '队列状态测试', method: 'testQueueStatus' }
    ];

    for (const test of tests) {
      try {
        console.log(`开始测试: ${test.name}`);
        const result = await this[test.method]();
        this.addTestResult(test.name, true, result);
        
        // 每个测试之间间隔500ms
        await this.delay(500);
      } catch (error) {
        console.error(`测试失败: ${test.name}`, error);
        this.addTestResult(test.name, false, error.message);
      }
    }

    wx.hideLoading();
    this.setData({ isRunning: false });

    wx.showToast({
      title: '测试完成',
      icon: 'success'
    });
  },

  /**
   * 测试手动错误上报
   */
  async testManualError() {
    const testError = new Error('这是一个测试错误');
    testError.stack = 'Error: 这是一个测试错误\n    at testManualError (errorTest.js:80:25)';
    
    app.reportError('manual_test_error', testError, {
      test_type: 'manual_error',
      test_time: new Date().toISOString(),
      test_data: { value: 123, text: 'test' }
    });

    return '手动错误上报已发送';
  },

  /**
   * 测试网络错误模拟
   */
  async testNetworkError() {
    try {
      // 模拟网络请求失败
      const request = require('../../utils/api/request');
      await request.request({
        url: '/test/nonexistent-endpoint',
        method: 'GET',
        showError: false,
        showLoading: false
      });
      return '网络请求意外成功';
    } catch (error) {
      // 这个错误会被request.js自动上报
      return '网络错误已被自动捕获和上报: ' + error.message;
    }
  },

  /**
   * 测试业务逻辑错误
   */
  async testBusinessError() {
    const businessError = new Error('业务逻辑处理失败');
    
    app.reportError('business_logic_test_error', businessError, {
      test_type: 'business_error',
      operation: 'data_processing',
      input_data: { id: 'test123' },
      expected_result: 'success',
      actual_result: 'failure'
    });

    return '业务逻辑错误上报已发送';
  },

  /**
   * 测试权限错误
   */
  async testPermissionError() {
    const permissionError = new Error('用户拒绝相册权限');
    
    app.reportError('permission_test_error', permissionError, {
      test_type: 'permission_error',
      permission_type: 'scope.writePhotosAlbum',
      user_action: 'save_image',
      denied_reason: 'user_refused'
    });

    return '权限错误上报已发送';
  },

  /**
   * 测试数据错误
   */
  async testDataError() {
    const dataError = new Error('数据格式不正确');
    
    app.reportError('data_validation_test_error', dataError, {
      test_type: 'data_error',
      data_type: 'resume_data',
      validation_rule: 'required_fields',
      missing_fields: ['name', 'phone']
    });

    return '数据错误上报已发送';
  },

  /**
   * 测试队列状态
   */
  async testQueueStatus() {
    try {
      const errorReporter = require('../../utils/error/errorReporter');
      const status = errorReporter.getQueueStatus();
      
      return `队列状态: 长度=${status.queue_length}, 上报中=${status.is_reporting}, 已启用=${status.is_enabled}`;
    } catch (error) {
      throw new Error('无法获取队列状态: ' + error.message);
    }
  },

  /**
   * 添加测试结果
   */
  addTestResult(testName, success, message) {
    const results = this.data.testResults;
    results.push({
      name: testName,
      success: success,
      message: message,
      time: new Date().toLocaleTimeString()
    });
    
    this.setData({ testResults: results });
  },

  /**
   * 延迟函数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  },

  /**
   * 清空测试结果
   */
  clearResults() {
    this.setData({ testResults: [] });
  },

  /**
   * 清空错误队列
   */
  clearErrorQueue() {
    try {
      const errorReporter = require('../../utils/error/errorReporter');
      errorReporter.clearQueue();
      
      wx.showToast({
        title: '队列已清空',
        icon: 'success'
      });
      
      this.checkErrorReporterStatus();
    } catch (error) {
      wx.showToast({
        title: '清空失败',
        icon: 'none'
      });
    }
  },

  /**
   * 触发JavaScript错误
   */
  triggerJSError() {
    // 这会触发全局错误监听器
    setTimeout(() => {
      throw new Error('这是一个故意触发的JavaScript错误，用于测试全局错误捕获');
    }, 100);
    
    wx.showToast({
      title: 'JS错误已触发',
      icon: 'none'
    });
  },

  /**
   * 触发Promise拒绝
   */
  triggerPromiseRejection() {
    // 这会触发未处理的Promise拒绝监听器
    new Promise((resolve, reject) => {
      setTimeout(() => {
        reject(new Error('这是一个故意触发的Promise拒绝，用于测试Promise错误捕获'));
      }, 100);
    });
    
    wx.showToast({
      title: 'Promise拒绝已触发',
      icon: 'none'
    });
  },

  /**
   * 模拟内存警告
   */
  simulateMemoryWarning() {
    // 创建大量数据模拟内存压力
    const largeArray = [];
    for (let i = 0; i < 100000; i++) {
      largeArray.push({
        id: i,
        data: 'x'.repeat(1000),
        timestamp: new Date().toISOString()
      });
    }
    
    // 手动上报内存警告
    app.reportError('simulated_memory_warning', '模拟内存警告测试', {
      test_type: 'memory_warning',
      array_length: largeArray.length,
      estimated_memory: '约100MB'
    });
    
    wx.showToast({
      title: '内存警告已模拟',
      icon: 'none'
    });
    
    // 清理内存
    setTimeout(() => {
      largeArray.length = 0;
    }, 1000);
  },

  /**
   * 刷新状态
   */
  refreshStatus() {
    this.checkErrorReporterStatus();
    wx.showToast({
      title: '状态已刷新',
      icon: 'success'
    });
  },

  /**
   * 单独测试方法
   */
  async testManualErrorSingle() {
    try {
      const result = await this.testManualError();
      this.addTestResult('手动错误上报测试', true, result);
    } catch (error) {
      this.addTestResult('手动错误上报测试', false, error.message);
    }
  },

  async testNetworkErrorSingle() {
    try {
      const result = await this.testNetworkError();
      this.addTestResult('网络错误模拟测试', true, result);
    } catch (error) {
      this.addTestResult('网络错误模拟测试', false, error.message);
    }
  },

  async testBusinessErrorSingle() {
    try {
      const result = await this.testBusinessError();
      this.addTestResult('业务逻辑错误测试', true, result);
    } catch (error) {
      this.addTestResult('业务逻辑错误测试', false, error.message);
    }
  },

  async testPermissionErrorSingle() {
    try {
      const result = await this.testPermissionError();
      this.addTestResult('权限错误测试', true, result);
    } catch (error) {
      this.addTestResult('权限错误测试', false, error.message);
    }
  }
});
