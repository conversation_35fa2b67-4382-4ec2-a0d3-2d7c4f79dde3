/* pages/errorTest/errorTest.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面标题 */
.header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 30rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  color: white;
}

.title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 24rpx;
  opacity: 0.9;
}

/* 通用区块样式 */
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-left: 10rpx;
  border-left: 6rpx solid #667eea;
}

/* 状态区块 */
.status-section {
  margin-bottom: 40rpx;
}

.status-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
  margin-bottom: 20rpx;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.status-item:last-child {
  margin-bottom: 0;
}

.status-label {
  font-size: 28rpx;
  color: #666;
}

.status-value {
  font-size: 28rpx;
  font-weight: bold;
}

.status-value.success {
  color: #52c41a;
}

.status-value.error {
  color: #ff4d4f;
}

.error-message {
  margin-top: 15rpx;
  padding: 15rpx;
  background-color: #fff2f0;
  border-radius: 8rpx;
  border-left: 4rpx solid #ff4d4f;
}

.error-message text {
  color: #ff4d4f;
  font-size: 24rpx;
}

.refresh-btn {
  width: 100%;
  background-color: #667eea;
  color: white;
  border-radius: 12rpx;
  font-size: 28rpx;
  padding: 20rpx;
}

/* 控制区块 */
.control-section {
  margin-bottom: 40rpx;
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.test-btn {
  padding: 25rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
}

.test-btn.primary {
  background-color: #52c41a;
  color: white;
}

.test-btn:not(.primary) {
  background-color: white;
  color: #333;
  border: 2rpx solid #d9d9d9;
}

.test-btn[disabled] {
  background-color: #f5f5f5;
  color: #bfbfbf;
}

/* 单项测试 */
.individual-tests {
  margin-bottom: 40rpx;
}

.test-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15rpx;
}

.test-item-btn {
  padding: 20rpx;
  background-color: white;
  border: 2rpx solid #e6f7ff;
  border-radius: 12rpx;
  font-size: 24rpx;
  color: #1890ff;
  text-align: center;
}

.test-item-btn:active {
  background-color: #e6f7ff;
}

/* 测试结果 */
.results-section {
  margin-bottom: 40rpx;
}

.results-list {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.result-item {
  padding: 25rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.result-item:last-child {
  border-bottom: none;
}

.result-item.success {
  border-left: 6rpx solid #52c41a;
}

.result-item.error {
  border-left: 6rpx solid #ff4d4f;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.result-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.result-time {
  font-size: 22rpx;
  color: #999;
}

.result-status {
  display: flex;
  align-items: flex-start;
  gap: 10rpx;
}

.status-icon {
  font-size: 24rpx;
  line-height: 1;
}

.result-message {
  font-size: 24rpx;
  color: #666;
  flex: 1;
  word-break: break-all;
}

/* 帮助区块 */
.help-section {
  margin-bottom: 40rpx;
}

.help-content {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.help-text {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 15rpx;
  padding-left: 20rpx;
  position: relative;
}

.help-text:before {
  content: "•";
  position: absolute;
  left: 0;
  color: #667eea;
  font-weight: bold;
}

.help-text:last-child {
  margin-bottom: 0;
}
