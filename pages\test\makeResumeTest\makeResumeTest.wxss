/* makeResume 页面修复测试样式 */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
  padding: 30rpx;
  background-color: #fff;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
}

.status-bar {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.status-text {
  font-size: 30rpx;
  color: #333;
}

.status-value {
  font-size: 30rpx;
  font-weight: bold;
  margin-left: 10rpx;
}

.status-value.ready {
  color: #007aff;
}

.status-value.running {
  color: #ff9500;
}

.status-value.completed {
  color: #34c759;
}

.controls {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.btn {
  flex: 1;
  height: 80rpx;
  border-radius: 10rpx;
  font-size: 30rpx;
  border: none;
}

.btn.primary {
  background-color: #007aff;
  color: #fff;
}

.btn.primary[disabled] {
  background-color: #ccc;
  color: #999;
}

.btn.secondary {
  background-color: #fff;
  color: #007aff;
  border: 2rpx solid #007aff;
}

.btn.secondary[disabled] {
  background-color: #f5f5f5;
  color: #ccc;
  border-color: #ccc;
}

.results-container {
  background-color: #fff;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
  overflow: hidden;
}

.results-header {
  padding: 30rpx;
  background-color: #f8f8f8;
  border-bottom: 1rpx solid #eee;
}

.results-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.results-list {
  max-height: 800rpx;
}

.result-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.result-item:last-child {
  border-bottom: none;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10rpx;
}

.result-message {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
}

.result-time {
  font-size: 24rpx;
  color: #999;
  margin-left: 20rpx;
}

.result-data {
  margin-top: 15rpx;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
}

.data-content {
  font-size: 24rpx;
  color: #666;
  font-family: monospace;
  word-break: break-all;
}

.empty-state {
  padding: 60rpx;
  text-align: center;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.footer {
  margin-top: 30rpx;
  text-align: center;
  padding: 20rpx;
}

.footer-text {
  font-size: 24rpx;
  color: #999;
}
