<!--makeResume 页面修复测试-->
<view class="container">
  <view class="header">
    <text class="title">makeResume 页面修复测试</text>
    <text class="subtitle">验证页面初始化逻辑修复效果</text>
  </view>

  <view class="status-bar">
    <text class="status-text">测试状态: </text>
    <text class="status-value {{testStatus}}">
      {{testStatus === 'ready' ? '准备就绪' : testStatus === 'running' ? '运行中...' : '已完成'}}
    </text>
  </view>

  <view class="controls">
    <button class="btn primary" bindtap="rerunTests" disabled="{{testStatus === 'running'}}">
      重新测试
    </button>
    <button class="btn secondary" bindtap="clearResults" disabled="{{testStatus === 'running'}}">
      清除结果
    </button>
  </view>

  <view class="results-container">
    <view class="results-header">
      <text class="results-title">测试结果 ({{testResults.length}})</text>
    </view>
    
    <scroll-view class="results-list" scroll-y="true">
      <view class="result-item" wx:for="{{testResults}}" wx:key="index">
        <view class="result-header">
          <text class="result-message">{{item.message}}</text>
          <text class="result-time">{{item.timestamp}}</text>
        </view>
        <view class="result-data" wx:if="{{item.data}}">
          <text class="data-content">{{item.data}}</text>
        </view>
      </view>
      
      <view class="empty-state" wx:if="{{testResults.length === 0}}">
        <text class="empty-text">暂无测试结果</text>
      </view>
    </scroll-view>
  </view>

  <view class="footer">
    <text class="footer-text">此测试用于验证 makeResume 页面初始化逻辑修复效果</text>
  </view>
</view>
