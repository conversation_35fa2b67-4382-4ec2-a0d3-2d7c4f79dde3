# 错误监控系统完整指南

## 系统概述

已完成微信小程序的全面错误监控系统建设，包括自动错误捕获、手动错误上报、测试工具等完整功能。

## 🎯 监控覆盖范围

### 1. 自动错误监控
- **全局错误**: `wx.onError` 自动捕获
- **Promise拒绝**: `wx.onUnhandledRejection` 自动捕获  
- **内存警告**: `wx.onMemoryWarning` 自动监控

### 2. 网络层监控
- **API请求失败**: 网络连接、超时、服务器错误
- **认证失败**: Token过期、重新登录失败
- **重试机制**: 自动重试失败后上报

### 3. 业务功能监控
- **简历制作**: 数据获取、生成、保存失败
- **PDF生成**: 生成超时、服务器错误
- **证件照处理**: 图片处理、权限拒绝
- **免费模板**: 下载失败、链接缺失
- **用户反馈**: 提交失败

### 4. 数据存储监控
- **本地存储**: 读取、写入、删除失败
- **简历数据**: 加载、保存、同步错误
- **文件管理**: 目录读取、文件删除失败

### 5. 图片处理监控
- **图片压缩**: Canvas导出、格式转换失败
- **智能裁剪**: 处理失败、备用方案
- **文件转换**: Base64转换错误

### 6. 页面生命周期监控
- **页面加载**: onLoad初始化失败
- **页面显示**: onShow数据刷新失败
- **页面渲染**: onReady组件初始化失败

## 📊 错误分类体系

### 网络相关错误
- `api_network_error`: API网络连接失败
- `api_business_error`: API业务逻辑错误(500+)
- `api_request_failed`: API重试失败
- `auth_relogin_failed`: 重新登录失败

### 业务功能错误
- `resume_data_error`: 简历数据相关错误
- `resume_generate_error`: 简历生成失败
- `pdf_generate_error`: PDF生成失败
- `idphoto_process_error`: 证件照处理失败
- `free_template_fetch_error`: 免费模板获取失败

### 数据存储错误
- `resume_data_load_error`: 简历数据加载失败
- `resume_data_save_error`: 简历数据保存失败
- `storage_read_dir_error`: 目录读取失败
- `storage_delete_file_error`: 文件删除失败

### 图片处理错误
- `image_compression_error`: 图片压缩失败
- `canvas_export_error`: Canvas导出失败
- `image_smart_process_error`: 智能图片处理失败
- `image_process_complete_failure`: 图片处理完全失败

### 页面生命周期错误
- `page_init_error`: 页面初始化失败
- `page_load_error`: 页面加载失败
- `page_show_error`: 页面显示失败
- `page_ready_error`: 页面渲染完成失败

### 权限相关错误
- `idphoto_permission_denied`: 证件照权限拒绝
- `idphoto_setting_denied`: 设置权限拒绝

### 用户操作错误
- `feedback_submit_error`: 反馈提交失败
- `auto_login_failed`: 自动登录失败
- `wx_login_failed`: 微信登录失败

## 🛠️ 使用方法

### 1. 手动上报错误

```javascript
const app = getApp();

// 基本用法
app.reportError('custom_error', error, {
  page: 'current_page',
  action: 'specific_action',
  custom_data: 'additional_info'
});

// 详细上下文
app.reportError('validation_error', error, {
  page: 'makeResume',
  action: 'save_resume',
  field_name: 'phone',
  field_value: 'invalid_phone',
  validation_rule: 'phone_format'
});
```

### 2. 在API调用中使用

```javascript
try {
  const result = await someApi.call();
} catch (error) {
  // 网络层已自动上报，这里处理特定业务错误
  if (error.statusCode === 400) {
    app.reportError('validation_error', error, {
      api: 'some_api',
      validation_field: 'specific_field'
    });
  }
}
```

### 3. 在页面生命周期中使用

```javascript
Page({
  onLoad(options) {
    try {
      this.initializePage();
    } catch (error) {
      const app = getApp();
      app.reportError('page_init_error', error, {
        page: 'current_page',
        action: 'onLoad',
        options: options
      });
    }
  }
});
```

## 🧪 测试工具

### 1. 错误测试页面
访问 `pages/errorTest/errorTest` 进行可视化测试：
- 运行所有测试
- 单项功能测试
- 查看测试结果
- 管理错误队列

### 2. 控制台测试工具

```javascript
// 获取测试工具
const testHelper = getApp().getErrorTestHelper();

// 快速测试
testHelper.quickErrorTest();

// 批量测试
testHelper.batchErrorTest(10);

// 测试不同错误类型
testHelper.testErrorTypes();

// 模拟真实场景
testHelper.simulateRealScenarios();

// 检查状态
testHelper.checkErrorReporterStatus();

// 清空队列
testHelper.clearErrorQueue();
```

### 3. 开发调试

```javascript
// 在任何页面的控制台中
const app = getApp();

// 手动触发测试
app.errorTestHelper.quickErrorTest();

// 检查错误上报器状态
app.errorTestHelper.checkErrorReporterStatus();
```

## 📈 监控数据分析

### 错误报告格式
```json
{
  "error_type": "api_network_error",
  "error_message": "网络连接失败",
  "error_stack": "Error stack...",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "system_info": {
    "platform": "ios",
    "model": "iPhone 14",
    "wechat_version": "8.0.32"
  },
  "user_info": {
    "user_id": "user123",
    "is_logged_in": true
  },
  "app_info": {
    "version": "1.0.0",
    "path": "pages/makeResume/makeResume"
  },
  "context": {
    "page": "makeResume",
    "action": "generateResume",
    "custom_data": "..."
  }
}
```

### 服务端接口
- **错误上报**: `POST /error-report`
- **批量上报**: `POST /error-report/batch`
- **错误统计**: `GET /error-report/stats`

## 🔧 配置管理

### 启用/禁用错误上报
```javascript
const errorReporter = require('./utils/error/errorReporter');

// 禁用错误上报（开发环境）
errorReporter.setEnabled(false);

// 启用错误上报（生产环境）
errorReporter.setEnabled(true);
```

### 队列管理
```javascript
// 查看队列状态
const status = errorReporter.getQueueStatus();

// 清空队列
errorReporter.clearQueue();
```

## 📋 最佳实践

### 1. 错误分类规范
- 使用统一的错误类型命名
- 提供详细的上下文信息
- 避免包含敏感信息

### 2. 性能考虑
- 错误上报不影响主要功能
- 队列管理避免内存泄漏
- 后台上报不阻塞用户操作

### 3. 调试建议
- 开发环境使用测试工具验证
- 生产环境监控错误频率
- 定期分析错误趋势

## 🚀 部署检查清单

### 上线前必检项目
- [ ] 错误上报接口已配置
- [ ] 服务端接口正常响应
- [ ] 测试工具验证通过
- [ ] 错误分类体系完整
- [ ] 监控覆盖率达标

### 上线后监控
- [ ] 错误频率监控
- [ ] 关键错误告警
- [ ] 用户影响分析
- [ ] 系统稳定性评估

## 总结

通过完整的错误监控系统，现在能够：

1. **全面监控**: 覆盖网络、业务、存储、图片、页面等各个层面
2. **主动发现**: 在用户反馈前主动发现问题
3. **快速定位**: 详细上下文信息帮助快速定位
4. **数据驱动**: 基于真实错误数据优化产品
5. **开发友好**: 完善的测试工具支持开发调试

这套监控系统将大大提升小程序的稳定性和用户体验！
