const imageCompressor = require('../../../utils/imageCompressor');
const smartImageCropper = require('../../../utils/smartImageCropper');
const ResumeFormHelper = require('../../../utils/resume/ResumeFormHelper.js');
const app = getApp();

Page({
  data: {
    basicInfoFormData: null, // 将在 onLoad 中初始化为 BasicInfo 实例
    genderArray: ['男', '女'],
    genderIndex: -1,
    marriageArray: ['未婚', '已婚', '离异', '丧偶'],
    marriageIndex: -1,
    politicsArray: ['群众', '共青团员', '中共党员', '民主党派'],
    politicsIndex: -1
  },

  onLoad() {
    console.log('=== 基本信息页面加载 ===');
    this.loadBasicInfoData();
  },

  /**
   * 从全局管理器加载基本信息数据
   */
  loadBasicInfoData() {
    try {
      const basicInfoData = ResumeFormHelper.loadFieldData('basicInfo', app);

      this.setData({
        basicInfoFormData: basicInfoData,
        genderIndex: this.data.genderArray.indexOf(basicInfoData.gender || ''),
        marriageIndex: this.data.marriageArray.indexOf(basicInfoData.marriage || ''),
        politicsIndex: this.data.politicsArray.indexOf(basicInfoData.politics || '')
      });

      console.log('✅ 基本信息数据加载成功:', basicInfoData);
    } catch (error) {
      console.error('❌ 加载基本信息数据失败:', error);
      // 出错时使用空数据
      const emptyBasicInfo = ResumeFormHelper.getEmptyFieldData('basicInfo');
      this.setData({
        basicInfoFormData: emptyBasicInfo,
        genderIndex: -1,
        marriageIndex: -1,
        politicsIndex: -1
      });
    }
  },

  // 处理输入框内容变化
  handleInput(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;

    this.setData({
      [`basicInfoFormData.${field}`]: value
    });
  },

  // 处理性别选择器
  handleGenderPicker(e) {
    const index = e.detail.value;
    this.setData({
      genderIndex: index,
      'basicInfoFormData.gender': this.data.genderArray[index]
    });
  },

  // 处理婚姻状况选择器
  handleMarriagePicker(e) {
    const index = parseInt(e.detail.value);
    if (index >= 0 && index < this.data.marriageArray.length) {
      this.setData({
        marriageIndex: index,
        'basicInfoFormData.marriage': this.data.marriageArray[index]
      });
    }
  },

  // 处理政治面貌选择器
  handlePoliticsPicker(e) {
    const index = parseInt(e.detail.value);
    if (index >= 0 && index < this.data.politicsArray.length) {
      this.setData({
        politicsIndex: index,
        'basicInfoFormData.politics': this.data.politicsArray[index]
      });
    }
  },

  // 处理日期选择器
  handlePicker(e) {
    const { field } = e.currentTarget.dataset;
    const value = e.detail.value;
    this.setData({
      [`basicInfoFormData.${field}`]: value
    });
  },

  // 清除字段内容
  clearField(e) {
    const { field } = e.currentTarget.dataset;
    this.setData({
      [`basicInfoFormData.${field}`]: ''
    });
  },

  // 清除自定义字段
  clearCustomField(e) {
    const { index } = e.currentTarget.dataset;
    this.setData({
      [`basicInfoFormData.customTitle${index}`]: '',
      [`basicInfoFormData.customContent${index}`]: ''
    });
  },

  // 选择照片
  chooseImage() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: async (res) => {
        // 获取图片路径
        const tempFilePath = res.tempFiles[0].tempFilePath;

        // 显示处理中提示
        wx.showLoading({
          title: '智能处理图片中...',
          mask: true
        });

        try {
          console.log('开始智能图片处理流程...');

          // 获取图片信息
          const imageInfo = await this.getImageInfo(tempFilePath);
          console.log('原始图片信息:', imageInfo);

          // 获取推荐的裁剪模式
          const recommendedMode = smartImageCropper.getRecommendedCropMode(imageInfo);
          console.log('推荐裁剪模式:', recommendedMode);

          // 计算目标尺寸（rpx转px）
          const systemInfo = wx.getSystemInfoSync();
          const targetWidth = Math.floor(120 * 2 / 750 * systemInfo.windowWidth);
          const targetHeight = Math.floor(150 * 2 / 750 * systemInfo.windowWidth);

          // 使用智能裁剪
          const croppedBase64 = await smartImageCropper.smartCrop(tempFilePath, {
            targetWidth: targetWidth,
            targetHeight: targetHeight,
            quality: 0.8,
            format: 'jpeg',
            cropMode: recommendedMode
          });

          console.log('智能裁剪完成');

          // 更新本地数据
          this.setData({
            'basicInfoFormData.photoUrl': croppedBase64
          });

          wx.hideLoading();
          wx.showToast({
            title: '图片处理成功',
            icon: 'success'
          });

        } catch (error) {
          console.error('智能处理失败，使用备用方案:', error);

          // 上报图片智能处理失败错误
          const app = getApp();
          app.reportError('image_smart_process_error', error, {
            page: 'basicInfo',
            action: 'chooseImage',
            step: 'smart_crop_failed'
          });

          // 备用方案：使用原有的裁剪方式
          this.fallbackImageProcess(tempFilePath);
        }
      },
      fail: (err) => {
        console.error('选择失败:', err);
        wx.showToast({
          title: '选择失败',
          icon: 'none'
        });
      }
    });
  },

  // 获取图片信息的Promise封装
  getImageInfo(filePath) {
    return new Promise((resolve, reject) => {
      wx.getImageInfo({
        src: filePath,
        success: resolve,
        fail: reject
      });
    });
  },

  // 备用图片处理方案
  async fallbackImageProcess(tempFilePath) {
    try {
      console.log('使用备用图片处理方案...');

      // 使用 wx.cropImage 图片裁剪
      const cropResult = await new Promise((resolve, reject) => {
        wx.cropImage({
          src: tempFilePath,
          cropScale: '3:4',  // 设置裁剪比例为 3:4
          success: resolve,
          fail: reject
        });
      });

      // 获取图片信息，用于智能压缩配置
      const imageInfo = await this.getImageInfo(cropResult.tempFilePath);
      console.log('裁剪后图片信息:', imageInfo);

      // 获取智能压缩建议
      const suggestion = imageCompressor.getCompressionSuggestion(
        imageInfo.width,
        imageInfo.height
      );
      console.log('压缩建议:', suggestion);

      // 使用图片压缩工具压缩图片
      const compressedBase64 = await imageCompressor.compressImage(cropResult.tempFilePath, {
        quality: suggestion.quality,
        maxWidth: suggestion.maxWidth,
        maxHeight: suggestion.maxHeight,
        format: 'jpeg'
      });

      // 更新本地数据
      this.setData({
        'basicInfoFormData.photoUrl': compressedBase64
      });

      wx.hideLoading();
      wx.showToast({
        title: '图片添加成功',
        icon: 'success'
      });

    } catch (error) {
      console.error('备用方案也失败:', error);

      // 上报图片处理完全失败错误
      const app = getApp();
      app.reportError('image_process_complete_failure', error, {
        page: 'basicInfo',
        action: 'fallbackImageProcess',
        step: 'all_methods_failed'
      });

      // 最后的降级方案
      this.setData({
        'basicInfoFormData.photoUrl': tempFilePath
      });

      wx.hideLoading();
      wx.showToast({
        title: '图片添加成功',
        icon: 'success'
      });
    }
  },

  /**
   * 保存基本信息
   */
  saveInfo() {
    try {
      // 使用 ResumeFormHelper 统一保存
      const success = ResumeFormHelper.saveFieldData('basicInfo', this.data.basicInfoFormData, app);

      if (success) {
        wx.showToast({
          title: '保存成功',
          icon: 'success',
          duration: 1500
        });

        console.log('✅ 基本信息保存成功:', this.data.basicInfoFormData);

        // 延迟返回，让用户看到保存成功的提示
        setTimeout(() => {
          wx.navigateBack({
            delta: 1  // 返回上一级页面
          });
        }, 500);
      } else {
        wx.showToast({
          title: '保存失败',
          icon: 'none'
        });
        console.error('❌ 基本信息保存失败');
      }
    } catch (error) {
      console.error('❌ 保存基本信息时发生错误:', error);
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    }
  },

  /**
   * 删除基本信息
   */
  deleteInfo() {
    wx.showModal({
      title: '提示',
      content: '确定要删除所有信息吗？',
      success: (res) => {
        if (res.confirm) {
          try {
            // 使用 ResumeFormHelper 清空数据
            const success = ResumeFormHelper.clearFieldData('basicInfo', app);

            if (success) {
              // 更新页面显示
              const emptyBasicInfo = ResumeFormHelper.getEmptyFieldData('basicInfo');
              this.setData({
                basicInfoFormData: emptyBasicInfo,
                genderIndex: -1,
                marriageIndex: -1,
                politicsIndex: -1
              });

              wx.showToast({
                title: '已删除',
                icon: 'success',
                duration: 1500
              });

              console.log('✅ 基本信息删除成功');

              // 延迟返回，让用户看到删除成功的提示
              setTimeout(() => {
                // 返回上一页并刷新数据
                const pages = getCurrentPages();
                const prePage = pages[pages.length - 2];
                if (prePage) {
                  prePage.onShow(); // 调用上一页的onShow方法刷新数据
                }
                wx.navigateBack({
                  delta: 1
                });
              }, 500);
            } else {
              wx.showToast({
                title: '删除失败',
                icon: 'none'
              });
              console.error('❌ 基本信息删除失败');
            }
          } catch (error) {
            console.error('❌ 删除基本信息时发生错误:', error);
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            });
          }
        }
      }
    });
  }
});