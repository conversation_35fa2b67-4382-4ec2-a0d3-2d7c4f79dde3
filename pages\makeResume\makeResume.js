const app = getApp();
const simpleDataFormatter = require('../../utils/resume/simpleDataFormatter');
const imageCompressor = require('../../utils/imageCompressor');
const smartImageCropper = require('../../utils/smartImageCropper');

Page({
  data: {
    modules: [
      { id: 1, type: 'basicInfo', name: '基本信息' },
      { id: 2, type: 'jobIntention', name: '求职意向' },
      { id: 3, type: 'education', name: '教育经历' },
      { id: 4, type: 'school', name: '在校经历' },
      { id: 5, type: 'internship', name: '实习经历' },
      { id: 6, type: 'work', name: '工作经历' },
      { id: 7, type: 'project', name: '项目经历' },
      { id: 8, type: 'skills', name: '技能特长' },
      { id: 9, type: 'awards', name: '奖项证书' },
      { id: 10, type: 'interests', name: '兴趣爱好' },
      { id: 11, type: 'evaluation', name: '自我评价' },
      { id: 12, type: 'custom1', name: '定制模块' },
      { id: 13, type: 'custom2', name: '定制模块' },
      { id: 14, type: 'custom3', name: '定制模块' }
    ],
    availableModulesToAdd: [], // 用于"添加更多模块"区域显示
    activeModules: [],
    moduleOrders: {},

    // 当前简历实例（直接引用，不复制数据）
    currentResume: null,

    // 证件照操作浮窗状态
    showPhotoActions: false
  },

  // 添加onLoad生命周期函数
  onLoad(options) {
    console.log('========== makeResume页面onLoad ==========');
    console.log('页面参数:', options);

    try {
      // 检查应用实例是否存在
      if (!app) {
        throw new Error('无法获取应用实例');
      }

      console.log('页面初始化完成');
    } catch (error) {
      console.error('makeResume页面初始化失败:', error);

      // 上报页面初始化失败错误
      if (app && app.reportError) {
        app.reportError('page_init_error', error, {
          page: 'makeResume',
          action: 'onLoad',
          step: 'initialization',
          options: options
        });
      }

      wx.showToast({
        title: '页面初始化失败',
        icon: 'none'
      });
    }
  },

  // 下拉刷新
  onPullDownRefresh() {
    // 重新加载数据
    this.refreshData();

    // 停止下拉刷新动画
    wx.stopPullDownRefresh();
  },

  // 页面显示时加载数据
  onShow() {
    console.log('========== makeResume页面onShow ==========');

    try {
      // 重新加载简历数据（可能在其他页面被修改）
      this.refreshData();
    } catch (error) {
      console.error('makeResume页面onShow失败:', error);

      // 上报页面显示失败错误
      if (app && app.reportError) {
        app.reportError('page_show_error', error, {
          page: 'makeResume',
          action: 'onShow',
          step: 'refresh_data'
        });
      }
    }
  },

  // 刷新数据
  refreshData() {
    this.loadResumeData();
    // 过滤掉已经有数据的模块，不在添加更多模块区域显示
    this.filterAvailableModulesToAdd();
  },

  // 加载简历数据
  loadResumeData() {
    console.log('========== loadResumeData 开始 ==========');

    try {
      // 通过全局简历管理器获取当前简历数据
      const resumeManager = app.getResumeManager();
      const currentResume = resumeManager.getCurrentResume();

      if (!currentResume) {
        console.error('无法获取当前简历数据');

        // 上报简历数据获取失败错误
        app.reportError('resume_data_load_error', '无法获取当前简历数据', {
          page: 'makeResume',
          action: 'loadResumeData',
          step: 'get_current_resume'
        });

        this.setData({ currentResume: null });
        return;
      }

      console.log('从全局简历管理器加载的数据:');
      console.log('当前简历索引:', resumeManager.currentResumeIndex);
      console.log('basicInfo:', currentResume.basicInfo.toObject());
      console.log('education:', currentResume.education.map(item => item.toObject()));
      console.log('skills:', currentResume.skills.toArray());

      // 直接设置当前简历实例的引用
      this.setData({
        currentResume: currentResume
      });

      // 更新格式化数据供WXML使用
      this.updateFormattedData();

      console.log('========== loadResumeData 结束 ==========');
    } catch (error) {
      console.error('加载简历数据失败:', error);
      // 如果出错，设置空数据
      this.setData({
        currentResume: null
      });
    }
  },

  // 更新格式化数据供WXML使用
  updateFormattedData() {
    const currentResume = this.data.currentResume;
    if (!currentResume) return;

    this.setData({
      basicInfo: currentResume.basicInfo.toObject(),
      jobIntention: currentResume.jobIntention.toObject(),
      education: currentResume.education.map(item => item.toObject()),
      school: currentResume.school.map(item => item.toObject()),
      internship: currentResume.internship.map(item => item.toObject()),
      work: currentResume.work.map(item => item.toObject()),
      project: currentResume.project.map(item => item.toObject()),
      skills: currentResume.skills.toArray(),
      awards: currentResume.awards.toArray(),
      interests: currentResume.interests.toArray(),
      evaluation: currentResume.evaluation,
      custom1: currentResume.custom1.map(item => item.toObject()),
      custom2: currentResume.custom2.map(item => item.toObject()),
      custom3: currentResume.custom3.map(item => item.toObject())
    });
  },

  // 保存简历数据
  saveResumeData() {
    console.log('=== 保存简历数据 ===');

    try {
      // 通过全局简历管理器保存当前简历
      const resumeManager = app.getResumeManager();
      resumeManager.saveToStorage();
      console.log('简历数据保存成功');
    } catch (error) {
      console.error('保存简历数据时出错:', error);
    }
  },



  // 过滤出可以添加的模块
  filterAvailableModulesToAdd() {
    console.log('========== filterAvailableModulesToAdd 开始 ==========');
    const allModules = this.data.modules;
    console.log('所有模块:', allModules);

    // 从当前简历实例获取数据和模块顺序
    const currentResume = this.data.currentResume;
    if (!currentResume) {
      console.error('当前简历实例不存在');
      return;
    }

    // 首先更新自定义模块名称
    this.updateCustomModuleNames(currentResume);

    let moduleOrders = currentResume.moduleOrders || {};
    console.log('模块顺序:', moduleOrders);

    console.log('========== 检查各模块数据 ==========');
    console.log('currentResume:', currentResume);
    console.log('basicInfo:', currentResume.basicInfo);
    console.log('basicInfo.name:', currentResume.basicInfo.name);
    console.log('education:', currentResume.education);
    console.log('skills:', currentResume.skills);
    console.log('skills.toArray():', currentResume.skills.toArray());

    // 创建一个已有内容的模块类型数组
    const filledModules = [];

    console.log('========== 开始检查模块内容 ==========');

    // 检查基本信息 - 基本信息模块始终显示
    filledModules.push('basicInfo');

    // 检查求职意向
    const jobIntention = currentResume.jobIntention;
    if (jobIntention && (jobIntention.position || jobIntention.location || jobIntention.salary || jobIntention.status)) {
      filledModules.push('jobIntention');
    }

    // 检查教育经历
    if (currentResume.education && currentResume.education.length > 0) {
      filledModules.push('education');
    }

    // 检查在校经历
    if (currentResume.school && currentResume.school.length > 0) {
      filledModules.push('school');
    }

    // 检查实习经历
    if (currentResume.internship && currentResume.internship.length > 0) {
      filledModules.push('internship');
    }

    // 检查工作经历
    if (currentResume.work && currentResume.work.length > 0) {
      filledModules.push('work');
    }

    // 检查项目经历
    if (currentResume.project && currentResume.project.length > 0) {
      filledModules.push('project');
    }

    // 检查技能特长
    if (currentResume.skills && currentResume.skills.toArray().length > 0) {
      filledModules.push('skills');
    }

    // 检查奖项证书
    if (currentResume.awards && currentResume.awards.toArray().length > 0) {
      filledModules.push('awards');
    }

    // 检查兴趣爱好
    if (currentResume.interests && currentResume.interests.toArray().length > 0) {
      filledModules.push('interests');
    }

    // 检查自我评价
    if (currentResume.evaluation && currentResume.evaluation.length > 0) {
      filledModules.push('evaluation');
    }

    // 检查自定义模块
    if (currentResume.custom1 && currentResume.custom1.length > 0 &&
        currentResume.custom1.some(item => item.customName || item.content || item.role)) {
      filledModules.push('custom1');
    }
    if (currentResume.custom2 && currentResume.custom2.length > 0 &&
        currentResume.custom2.some(item => item.customName || item.content || item.role)) {
      filledModules.push('custom2');
    }
    if (currentResume.custom3 && currentResume.custom3.length > 0 &&
        currentResume.custom3.some(item => item.customName || item.content || item.role)) {
      filledModules.push('custom3');
    }

    // completeFillModuleOrders
    moduleOrders = this.completeFillModuleOrders(moduleOrders);

    // 过滤，只保留没有内容的模块
    const emptyModules = allModules.filter(module =>
      !filledModules.includes(module.type)
    );

    let activeModules = allModules.filter(module =>
      filledModules.includes(module.type)
    );

    activeModules = activeModules.sort((a, b) => {
      const orderA = moduleOrders[a.type] !== undefined ? moduleOrders[a.type] : Infinity;
      const orderB = moduleOrders[b.type] !== undefined ? moduleOrders[b.type] : Infinity;
      return orderA - orderB;
    });

    console.log('========== 过滤结果 ==========');
    console.log('filledModules:', filledModules);
    console.log('activeModules:', activeModules);
    console.log('activeModules.length:', activeModules.length);
    console.log('emptyModules:', emptyModules);
    console.log('emptyModules.length:', emptyModules.length);
    console.log('moduleOrders:', moduleOrders);

    // 更新data
    this.setData({
      availableModulesToAdd: emptyModules,
      activeModules: activeModules,
      moduleOrders: moduleOrders
    }, () => {
      console.log('========== setData完成后的最终状态 ==========');
      console.log('this.data.activeModules:', this.data.activeModules);
      console.log('this.data.activeModules.length:', this.data.activeModules.length);
      console.log('this.data.availableModulesToAdd:', this.data.availableModulesToAdd);
      console.log('this.data.availableModulesToAdd.length:', this.data.availableModulesToAdd.length);
      console.log('========== filterAvailableModulesToAdd 结束 ==========');
    });

    // 更新简历实例中的模块顺序
    currentResume.moduleOrders = moduleOrders;
    const resumeManager = app.getResumeManager();
    resumeManager.saveToStorage();
  },

  completeFillModuleOrders(moduleOrders) {

    console.log('============= moduleOrders before sort ============================');
    console.log('moduleOrders:', moduleOrders);

    // 更新moduleOrders
    let maxOrder = 0;
    this.data.modules.forEach(module => {
      if (moduleOrders[module.type]) {
        maxOrder = Math.max(maxOrder, moduleOrders[module.type]);
      }
    });
    console.log('maxOrder:', maxOrder);
    // 根据moduleOrders排序

    // 遍历 modules 数组
    this.data.modules.forEach(module => {
        const type = module.type;
        // 如果 moduleOrders 中不存在该类型，则添加并分配新的顺序值
        if (moduleOrders[type] === undefined) {
            maxOrder++;
            moduleOrders[type] = maxOrder;
        }
    });

    console.log('============= moduleOrders after sort ============================');
    console.log('moduleOrders:', moduleOrders);
    return moduleOrders;
  },

  // 更新自定义模块名称
  updateCustomModuleNames: function(currentResume) {
    console.log('========== 更新自定义模块名称 (makeResume) ==========');

    const modules = [...this.data.modules]; // 创建副本

    // 更新custom1模块名称
    if (currentResume.custom1 && currentResume.custom1.length > 0) {
      const custom1Data = currentResume.custom1[0];
      if (custom1Data.customName && custom1Data.customName.trim()) {
        const custom1Module = modules.find(m => m.type === 'custom1');
        if (custom1Module) {
          custom1Module.name = custom1Data.customName;
          console.log('更新custom1模块名称:', custom1Data.customName);
        }
      }
    }

    // 更新custom2模块名称
    if (currentResume.custom2 && currentResume.custom2.length > 0) {
      const custom2Data = currentResume.custom2[0];
      if (custom2Data.customName && custom2Data.customName.trim()) {
        const custom2Module = modules.find(m => m.type === 'custom2');
        if (custom2Module) {
          custom2Module.name = custom2Data.customName;
          console.log('更新custom2模块名称:', custom2Data.customName);
        }
      }
    }

    // 更新custom3模块名称
    if (currentResume.custom3 && currentResume.custom3.length > 0) {
      const custom3Data = currentResume.custom3[0];
      if (custom3Data.customName && custom3Data.customName.trim()) {
        const custom3Module = modules.find(m => m.type === 'custom3');
        if (custom3Module) {
          custom3Module.name = custom3Data.customName;
          console.log('更新custom3模块名称:', custom3Data.customName);
        }
      }
    }

    // 更新data中的modules
    this.setData({
      modules: modules
    });

    console.log('========== 自定义模块名称更新完成 (makeResume) ==========');
  },

  // 添加证件照（已合并到chooseImage方法中）
  addPhoto() {
    this.chooseImage();
  },

  // 添加模块
  addModule(e) {
    const moduleType = e.currentTarget.dataset.type;
    // 根据不同的模块类型跳转到不同的页面
    switch (moduleType) {
      case 'basicInfo':
        wx.navigateTo({
          url: '/pages/makeResume/basicInfo/basicInfo'
        });
        break;
      case 'jobIntention':
        wx.navigateTo({
          url: '/pages/makeResume/jobIntention/jobIntention'
        });
        break;

      case 'education':
        wx.navigateTo({
          url: '/pages/makeResume/education/education/education'
        });
        break;
      case 'school':
        wx.navigateTo({
          url: '/pages/makeResume/school/school/school'
        });
        break;
      case 'internship':
        wx.navigateTo({
          url: '/pages/makeResume/internship/internshipExperience/internshipExperience'
        });
        break;
      case 'work':
        wx.navigateTo({
          url: '/pages/makeResume/work/work/work'
        });
        break;
      case 'project':  // 添加项目经历的处理
        wx.navigateTo({
          url: '/pages/makeResume/project/project/project'
        });
        break;
      case 'skills':
        wx.navigateTo({
          url: '/pages/makeResume/skills/skills'
        });
        break;
      case 'awards':  // 添加奖项证书的处理
        wx.navigateTo({
          url: '/pages/makeResume/awards/awards'
        });
        break;
      case 'interests':
        wx.navigateTo({
          url: '/pages/makeResume/interests/interests'
        });
        break;
      case 'evaluation':
        wx.navigateTo({
          url: '/pages/makeResume/evaluation/evaluation'
        });
        break;
      // 添加自定义模块的跳转逻辑
      case 'custom1':
        wx.navigateTo({
          url: '/pages/makeResume/custom/custom1/custom1'
        });
        break;
      case 'custom2':
        wx.navigateTo({
          url: '/pages/makeResume/custom/custom2/custom2'
        });
        break;
      case 'custom3':
        wx.navigateTo({
          url: '/pages/makeResume/custom/custom3/custom3'
        });
        break;
      // 其他模块的处理可以继续添加
      default:
        console.warn('未知的添加模块类型:', moduleType);
        break;
    }
  },

  // 模块管理
  handleModules() {
    wx.navigateTo({
      url: '/pages/makeResume/moduleManage/moduleManage'
    });
  },

  // AI智能优化
  handleAI() {
    wx.showToast({
      title: '该功能开发中......',
      icon: 'none',
      duration: 2000
    });
  },

  // 生成简历
  generateResume() {
    try {
      // 通过全局简历管理器获取当前简历数据
      const resumeManager = app.getResumeManager();
      const currentResume = resumeManager.getCurrentResume();

      if (!currentResume) {
        // 上报数据获取失败错误
        app.reportError('resume_data_error', '无法获取简历数据', {
          page: 'makeResume',
          action: 'generateResume',
          step: 'get_current_resume'
        });

        wx.showToast({
          title: '无法获取简历数据',
          icon: 'none'
        });
        return;
      }

      // 验证个人基本信息必填项
      const basicInfo = currentResume.basicInfo;
      if (!basicInfo || !basicInfo.name || !basicInfo.name.trim()) {
        wx.showModal({
          title: '提示',
          content: '请先完善个人基本信息中的姓名',
          showCancel: false,
          confirmText: '去完善',
          success: () => {
            // 跳转到基本信息页面
            wx.navigateTo({
              url: '/pages/makeResume/basicInfo/basicInfo'
            });
          }
        });
        return;
      }

      if (!basicInfo.phone || !basicInfo.phone.trim()) {
        wx.showModal({
          title: '提示',
          content: '请先完善个人基本信息中的联系电话',
          showCancel: false,
          confirmText: '去完善',
          success: () => {
            // 跳转到基本信息页面
            wx.navigateTo({
              url: '/pages/makeResume/basicInfo/basicInfo'
            });
          }
        });
        return;
      }

      // 确保模块顺序存在
      let moduleOrders = currentResume.moduleOrders;
      if (!moduleOrders || Object.keys(moduleOrders).length === 0) {
        moduleOrders = {};
        this.data.activeModules.forEach((module, index) => {
          moduleOrders[module.type] = index;
        });
        currentResume.moduleOrders = moduleOrders;
        resumeManager.saveToStorage();
      }

      // 构建简历数据结构（使用当前简历的数据）
      const pageData = currentResume.toObject();

      // 转换为服务端期望格式
      const resumeData = simpleDataFormatter.convertToServerFormat(pageData);

      console.log('========== makeResume传出的数据 ==========');
      console.log('基本信息：', resumeData.basicInfo);
      console.log('求职意向：', resumeData.jobIntention);
      console.log('教育经历：', resumeData.education);
      console.log('在校经历：', resumeData.school);
      console.log('实习经历：', resumeData.internship);
      console.log('工作经历：', resumeData.work);
      console.log('项目经历：', resumeData.project);
      console.log('技能特长：', resumeData.skills);
      console.log('获奖证书：', resumeData.awards);
      console.log('兴趣爱好：', resumeData.interests);
      console.log('自我评价：', resumeData.evaluation);
      console.log('自定义模块：', resumeData.custom1, resumeData.custom2, resumeData.custom3);
      console.log('==========================================');

      // 将数据转换为查询字符串
      const queryString = encodeURIComponent(JSON.stringify(resumeData));

      // 跳转并传递数据
      wx.navigateTo({
        url: `/pages/makeCreateResume/makeCreateResume?resumeData=${queryString}`,
        success: () => {
          console.log('跳转到预览页面成功');
        },
        fail: (err) => {
          console.error('跳转失败:', err);
          wx.showToast({
            title: '跳转失败',
            icon: 'none'
          });
        }
      });

    } catch (error) {
      console.error('生成简历失败:', error);

      // 上报生成简历失败错误
      app.reportError('resume_generate_error', error, {
        page: 'makeResume',
        action: 'generateResume',
        step: 'general_error'
      });

      wx.showToast({
        title: '生成简历失败',
        icon: 'none'
      });
    }
  },

  // 选择图片
  chooseImage() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: async (res) => {
        // 获取图片路径
        const tempFilePath = res.tempFiles[0].tempFilePath;

        // 显示处理中提示
        wx.showLoading({
          title: '智能处理图片中...',
          mask: true
        });

        try {
          console.log('开始智能图片处理流程...');

          // 获取图片信息
          const imageInfo = await this.getImageInfo(tempFilePath);
          console.log('原始图片信息:', imageInfo);

          // 获取推荐的裁剪模式
          const recommendedMode = smartImageCropper.getRecommendedCropMode(imageInfo);
          console.log('推荐裁剪模式:', recommendedMode);

          // 计算目标尺寸（rpx转px，假设设备像素比为2）
          const targetWidth = Math.floor(120 * 2 / 750 * wx.getSystemInfoSync().windowWidth);
          const targetHeight = Math.floor(150 * 2 / 750 * wx.getSystemInfoSync().windowWidth);

          // 使用智能裁剪
          const croppedBase64 = await smartImageCropper.smartCrop(tempFilePath, {
            targetWidth: targetWidth,
            targetHeight: targetHeight,
            quality: 0.8,
            format: 'jpeg',
            cropMode: recommendedMode
          });

          console.log('智能裁剪完成');

          // 直接更新简历实例数据
          const currentResume = this.data.currentResume;
          if (currentResume) {
            currentResume.basicInfo.photoUrl = croppedBase64;
            const resumeManager = app.getResumeManager();
            resumeManager.saveToStorage();

            // 更新格式化数据
            this.updateFormattedData();
          }

          wx.hideLoading();
          wx.showToast({
            title: '图片处理成功',
            icon: 'success'
          });

        } catch (error) {
          console.error('智能处理失败，使用备用方案:', error);

          // 上报图片智能处理失败错误
          app.reportError('image_smart_process_error', error, {
            page: 'makeResume',
            action: 'chooseImage',
            step: 'smart_crop_failed'
          });

          // 备用方案：使用原有的裁剪方式
          this.fallbackImageProcess(tempFilePath);
        }
      },
      fail: (err) => {
        console.error('选择失败:', err);
        // wx.showToast({
        //   title: '选择失败',
        //   icon: 'none'
        // });
      }
    });
  },

  // 获取图片信息的Promise封装
  getImageInfo(filePath) {
    return new Promise((resolve, reject) => {
      wx.getImageInfo({
        src: filePath,
        success: resolve,
        fail: reject
      });
    });
  },

  // 备用图片处理方案
  async fallbackImageProcess(tempFilePath) {
    try {
      console.log('使用备用图片处理方案...');

      // 使用 wx.cropImage 图片裁剪
      const cropResult = await new Promise((resolve, reject) => {
        wx.cropImage({
          src: tempFilePath,
          cropScale: '3:4',  // 设置裁剪比例为 3:4
          success: resolve,
          fail: reject
        });
      });

      // 获取图片信息，用于智能压缩配置
      const imageInfo = await this.getImageInfo(cropResult.tempFilePath);
      console.log('裁剪后图片信息:', imageInfo);

      // 获取智能压缩建议
      const suggestion = imageCompressor.getCompressionSuggestion(
        imageInfo.width,
        imageInfo.height
      );
      console.log('压缩建议:', suggestion);

      // 使用图片压缩工具压缩图片
      const compressedBase64 = await imageCompressor.compressImage(cropResult.tempFilePath, {
        quality: suggestion.quality,
        maxWidth: suggestion.maxWidth,
        maxHeight: suggestion.maxHeight,
        format: 'jpeg'
      });

      // 直接更新简历实例数据
      const currentResume = this.data.currentResume;
      if (currentResume) {
        currentResume.basicInfo.photoUrl = compressedBase64;
        const resumeManager = app.getResumeManager();
        resumeManager.saveToStorage();

        // 更新格式化数据
        this.updateFormattedData();
      }

      wx.hideLoading();
      wx.showToast({
        title: '图片添加成功',
        icon: 'success'
      });

    } catch (error) {
      console.error('备用方案也失败:', error);

      // 最后的降级方案
      try {
        const base64 = await this.imageToBase64(tempFilePath);

        // 直接更新简历实例数据
        const currentResume = this.data.currentResume;
        if (currentResume) {
          currentResume.basicInfo.photoUrl = base64;
          const resumeManager = app.getResumeManager();
          resumeManager.saveToStorage();

          // 更新格式化数据
          this.updateFormattedData();
        }

        wx.hideLoading();
        wx.showToast({
          title: '图片添加成功',
          icon: 'success'
        });
      } catch (finalError) {
        console.error('所有方案都失败:', finalError);

        // 上报图片处理完全失败错误
        app.reportError('image_process_complete_failure', finalError, {
          page: 'makeResume',
          action: 'fallbackImageProcess',
          step: 'all_methods_failed'
        });

        wx.hideLoading();
        wx.showToast({
          title: '图片处理失败',
          icon: 'none'
        });
      }
    }
  },

  // 图片转base64方法
  imageToBase64(filePath) {
    return new Promise((resolve, reject) => {
      try {
        // 获取文件后缀名
        const ext = filePath.substring(filePath.lastIndexOf('.') + 1).toLowerCase();

        // 根据后缀设置MIME类型
        let mimeType = 'image/jpeg'; // 默认类型
        switch (ext) {
            case 'png':
                mimeType = 'image/png';
                break;
            case 'gif':
                mimeType = 'image/gif';
                break;
            case 'webp':
                mimeType = 'image/webp';
                break;
            case 'jpg':
            case 'jpeg':
                mimeType = 'image/jpeg';
                break;
            default:
                console.log('未知图片类型，使用默认type: image/jpeg');
        }

        // 添加文件类型日志
        console.log('图片类型:', ext, 'MIME类型:', mimeType);

        wx.getFileSystemManager().readFile({
            filePath,
            encoding: 'base64',
            success: res => {
                // 添加详细的日志
                console.log('图片转换成功 ->', {
                    type: mimeType,
                    size: res.data.length,
                    path: filePath
                });
                resolve(`data:${mimeType};base64,` + res.data);
            },
            fail: error => {
                console.error('图片转base64失败:', error);
                reject(error);
            }
        });
      } catch (error) {
        console.error('图片处理过程出错:', error);
        reject(error);
      }
    });
  },

  // 统一处理模块点击事件
  handleModuleClick(e) {
    const moduleType = e.currentTarget.dataset.type;
    let url = '';

    switch (moduleType) {
      case 'basicInfo':
        url = '/pages/makeResume/basicInfo/basicInfo';
        break;
      case 'jobIntention':
        url = '/pages/makeResume/jobIntention/jobIntention';
        break;
      case 'education':
        url = '/pages/makeResume/education/education/education';
        break;
      case 'school':
        url = '/pages/makeResume/school/school/school';
        break;
      case 'internship':
        url = '/pages/makeResume/internship/internshipExperience/internshipExperience';
        break;
      case 'work':
        url = '/pages/makeResume/work/work/work';
        break;
      case 'project':
        url = '/pages/makeResume/project/project/project';
        break;
      case 'skills':
        url = '/pages/makeResume/skills/skills';
        break;
      case 'awards':
        url = '/pages/makeResume/awards/awards';
        break;
      case 'interests':
        url = '/pages/makeResume/interests/interests';
        break;
      case 'evaluation':
        url = '/pages/makeResume/evaluation/evaluation';
        break;
      case 'custom1':
        url = '/pages/makeResume/custom/custom1/custom1';
        break;
      case 'custom2':
        url = '/pages/makeResume/custom/custom2/custom2';
        break;
      case 'custom3':
        url = '/pages/makeResume/custom/custom3/custom3';
        break;
      default:
        console.warn('未知的模块类型:', moduleType);
        return;
    }

    if (url) {
      // 记录用户行为
      // if (app && app.trackUserAction) {
      //   app.trackUserAction('resume_edit', {
      //     moduleType: moduleType
      //   });
      // }

      wx.navigateTo({ url });
    }
  },

  // 显示证件照操作浮窗
  showPhotoActionSheet() {
    this.setData({
      showPhotoActions: true
    });
  },

  // 隐藏证件照操作浮窗
  hidePhotoActionSheet() {
    this.setData({
      showPhotoActions: false
    });
  },

  // 替换证件照
  replacePhoto() {
    this.hidePhotoActionSheet();
    // 延迟一点时间再调用选择图片，确保浮窗动画完成
    setTimeout(() => {
      this.chooseImage();
    }, 300);
  },

  // 删除证件照
  deletePhoto() {
    this.hidePhotoActionSheet();

    wx.showModal({
      title: '确认删除',
      content: '确定要删除当前证件照吗？',
      confirmText: '删除',
      confirmColor: '#ff4757',
      success: (res) => {
        if (res.confirm) {
          // 删除证件照
          const currentResume = this.data.currentResume;
          if (currentResume) {
            currentResume.basicInfo.photoUrl = '';
            const resumeManager = app.getResumeManager();
            resumeManager.saveToStorage();

            // 更新格式化数据
            this.updateFormattedData();

            wx.showToast({
              title: '删除成功',
              icon: 'success'
            });
          }
        }
      }
    });
  }
})