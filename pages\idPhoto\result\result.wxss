.container {
  padding: 40rpx 20rpx 20rpx 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}



/* 内联加载状态 */
.inline-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  min-height: 243rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e0e0e0;
  border-top: 4rpx solid #4B8BF5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 32rpx;
  color: #666;
}

/* 结果展示区域 */
.result-section {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

/* 证件照预览 */
.photo-preview {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.preview-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 图片容器 - 支持背景预览 */
.image-container {
  position: relative;
  display: inline-block;
}

/* 背景预览层 */
.background-layer {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  border-radius: 8rpx;
}

/* 透明背景图片层 */
.result-image {
  position: relative;
  z-index: 2;
  max-width: 600rpx;
  max-height: 800rpx;
  display: block;
}



/* 底色选择 */
.background-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.color-options {
  display: flex;
  flex-direction: row;
  gap: 20rpx;
  justify-content: flex-start;
  flex-wrap: wrap;
}

.color-square {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;
}

.color-square.selected {
  border: 3rpx solid rgba(75, 139, 245, 0.8);
  box-shadow: 0 8rpx 25rpx rgba(75, 139, 245, 0.3);
}

.color-fill {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

/* 操作按钮 */
.action-section {
  display: flex;
  justify-content: center;
  margin-top: 40rpx;
}

.save-btn {
  width: 100%;
  border-radius: 15rpx;
  font-size: 30rpx;
  font-weight: bold;
  border: none;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #4B8BF5 0%, #6BA3F7 100%);
  color: white;
  box-shadow: 0 8rpx 25rpx rgba(75, 139, 245, 0.4);
}

.save-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 15rpx rgba(75, 139, 245, 0.3);
}

.save-btn:disabled {
  background: #e0e0e0;
  color: #999;
  box-shadow: none;
}

/* 高清版本提示 */
.hd-section {
  background: linear-gradient(135deg, #e8f4fd 0%, #f0f9ff 100%);
  border-radius: 20rpx;
  padding: 30rpx;
  border: 1rpx solid #b3d9ff;
}

.hd-tip {
  text-align: center;
}

.hd-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #1976d2;
  display: block;
  margin-bottom: 8rpx;
}

.hd-desc {
  font-size: 24rpx;
  color: #1976d2;
  opacity: 0.8;
  display: block;
}


