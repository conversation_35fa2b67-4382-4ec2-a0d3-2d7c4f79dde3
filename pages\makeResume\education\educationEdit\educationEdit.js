const ResumeFormHelper = require('../../../../utils/resume/ResumeFormHelper.js');
const app = getApp();

Page({
  data: {
    educationEditFormData: null, // 将在 onLoad 中初始化为 EducationItem 实例
    editIndex: -1,
    degreeArray: ['高中', '大专', '本科', '硕士', '博士'],  // 学历选项
    degreeIndex: 2,    // 选中的学历索引，默认选中本科
    currentDate: '' // 当前日期，用于"至今"后重新选择日期时的默认值
  },

  onLoad(options) {
    console.log('=== 教育经历编辑页面加载 ===');

    // 初始化当前日期（格式：YYYY-MM）
    const now = new Date();
    const currentDate = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
    this.setData({
      currentDate: currentDate
    });

    if (options.index !== undefined) {
      // 编辑模式
      this.loadEducationItemForEdit(parseInt(options.index));
    } else {
      // 新增模式
      this.initNewEducationItem();
    }
  },

  /**
   * 加载教育经历项进行编辑
   */
  loadEducationItemForEdit(index) {
    try {
      const educationData = ResumeFormHelper.loadFieldData('education', app);

      if (educationData && educationData[index]) {
        const item = educationData[index];
        this.setData({
          educationEditFormData: item,
          editIndex: index,
          degreeIndex: this.data.degreeArray.indexOf(item.degree) || 2
        });
        console.log('✅ 教育经历编辑数据加载成功:', item);
      } else {
        console.error('❌ 教育经历数据不存在，索引:', index);
        this.initNewEducationItem();
      }
    } catch (error) {
      console.error('❌ 加载教育经历编辑数据失败:', error);
      this.initNewEducationItem();
    }
  },

  /**
   * 初始化新的教育经历项
   */
  initNewEducationItem() {
    const emptyEducationItem = ResumeFormHelper.getEmptyFieldData('educationItem');
    this.setData({
      educationEditFormData: emptyEducationItem,
      editIndex: -1,
      degreeIndex: 2 // 默认本科
    });
    console.log('✅ 新教育经历项初始化完成');
  },

  // 输入框内容变化时触发
  handleInput(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;

    this.setData({
      [`educationEditFormData.${field}`]: value
    });
  },

  // 学历选择变化时触发
  handleDegreeChange(e) {
    const index = e.detail.value;
    this.setData({
      'educationEditFormData.degree': this.data.degreeArray[index],
      degreeIndex: index
    });
  },

  // 日期选择变化时触发
  handleDateChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;

    this.setData({
      [`educationEditFormData.${field}`]: value
    });
  },

  // 设置结束时间为"至今"
  setEndDateToNow() {
    this.setData({
      'educationEditFormData.endDate': '至今'
    });
  },

  /**
   * 保存教育经历信息
   */
  saveEducation() {
    const { educationEditFormData, editIndex } = this.data;

    // 验证必填字段
    if (!educationEditFormData.school || !educationEditFormData.major || !educationEditFormData.degree) {
      wx.showToast({
        title: '请填写完整信息',
        icon: 'none'
      });
      return;
    }

    try {
      const educationData = ResumeFormHelper.loadFieldData('education', app);
      let educationList = Array.isArray(educationData) ? [...educationData] : [];

      if (editIndex >= 0) {
        // 更新已有记录
        educationList[editIndex] = educationEditFormData;
      } else {
        // 添加新记录
        educationList.push(educationEditFormData);
      }

      // 使用 ResumeFormHelper 统一保存
      const success = ResumeFormHelper.saveFieldData('education', educationList, app);

      if (success) {
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        });

        console.log('✅ 教育经历保存成功:', educationEditFormData);

        setTimeout(() => {
          wx.navigateBack();
        }, 500);
      } else {
        wx.showToast({
          title: '保存失败',
          icon: 'none'
        });
        console.error('❌ 教育经历保存失败');
      }
    } catch (error) {
      console.error('❌ 保存教育经历时发生错误:', error);
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    }
  },

  /**
   * 删除教育经历
   */
  deleteEducation() {
    const { editIndex } = this.data;
    if (editIndex >= 0) {
      wx.showModal({
        title: '提示',
        content: '确定删除这条教育经历吗？',
        success: (res) => {
          if (res.confirm) {
            try {
              const educationData = ResumeFormHelper.loadFieldData('education', app);
              let educationList = Array.isArray(educationData) ? [...educationData] : [];

              educationList.splice(editIndex, 1);

              // 使用 ResumeFormHelper 统一保存
              const success = ResumeFormHelper.saveFieldData('education', educationList, app);

              if (success) {
                wx.navigateBack({
                  success: () => {
                    wx.showToast({
                      title: '删除成功',
                      icon: 'success'
                    });
                  }
                });
                console.log('✅ 教育经历删除成功');
              } else {
                wx.showToast({
                  title: '删除失败',
                  icon: 'none'
                });
                console.error('❌ 教育经历删除失败');
              }
            } catch (error) {
              console.error('❌ 删除教育经历时发生错误:', error);
              wx.showToast({
                title: '删除失败',
                icon: 'none'
              });
            }
          }
        }
      });
    }
  }
});