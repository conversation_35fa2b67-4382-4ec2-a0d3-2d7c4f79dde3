/**
 * 简历样式页面下拉刷新功能测试
 * 测试修复后的下拉刷新功能，确保数据不会丢失
 */

// 模拟微信小程序环境
const mockWx = {
  showToast: (options) => {
    console.log('Toast:', options.title);
  },
  stopPullDownRefresh: () => {
    console.log('停止下拉刷新');
  }
};

// 模拟全局wx对象
global.wx = mockWx;

// 模拟API响应（直接返回templates数组，不包装在data中）
const mockApiResponses = {
  // 成功响应
  success: {
    templates: [
      { id: 'template1', thumb_url: 'http://example.com/1.jpg' },
      { id: 'template2', thumb_url: 'http://example.com/2.jpg' },
      { id: 'template3', thumb_url: 'http://example.com/3.jpg' },
      { id: 'template4', thumb_url: 'http://example.com/4.jpg' },
      { id: 'template5', thumb_url: 'http://example.com/5.jpg' },
      { id: 'template6', thumb_url: 'http://example.com/6.jpg' }
    ],
    total: 6,
    skip: 0,
    limit: 20,
    has_more: false
  },
  // 空响应
  empty: {
    templates: [],
    total: 0,
    skip: 0,
    limit: 20,
    has_more: false
  },
  // 错误响应
  error: null
};

// 模拟API模块
const mockResumeStyleApi = {
  getResumeStyleList: async (params) => {
    console.log('API调用参数:', params);
    
    // 模拟不同的响应场景
    const scenario = global.testScenario || 'success';
    
    switch (scenario) {
      case 'success':
        return mockApiResponses.success;
      case 'empty':
        return mockApiResponses.empty;
      case 'error':
        throw new Error('网络连接失败');
      default:
        return mockApiResponses.success;
    }
  }
};

// 模拟页面对象
function createMockPage() {
  const pageData = {
    templates: [],
    selectedTemplateId: '',
    selectedTemplate: null,
    isLoading: false,
    isRefreshing: false,
    isLoadingMore: false,
    hasMore: true,
    skip: 0,
    limit: 20,
    currentCategory: '',
    currentSort: 'popular'
  };

  const page = {
    data: pageData,
    setData: function(newData) {
      Object.assign(this.data, newData);
      console.log('页面数据更新:', Object.keys(newData));
    }
  };

  // 引入loadTemplates方法（简化版本，参考免费模板页面）
  page.loadTemplates = async function(isRefresh = false) {
    if (this.data.isLoading && !isRefresh) return;

    try {
      this.setData({
        isLoading: !isRefresh,
        isRefreshing: isRefresh
      });

      const skip = isRefresh ? 0 : this.data.skip;
      const params = {
        skip: skip,
        limit: this.data.limit,
        sort: this.data.currentSort
      };

      console.log('加载模板参数:', params);

      const response = await mockResumeStyleApi.getResumeStyleList(params);
      console.log('模板列表响应:', response);

      if (response && response.templates) {
        const newTemplates = (response.templates || []).map((template, index) => ({
          ...template,
          isLoading: true,
          imageError: false,
          uniqueKey: `${template.id}_${Date.now()}_${index}_${Math.random().toString(36).substring(2, 11)}`
        }));

        // 去重处理：基于id字段去重
        let finalTemplates;
        if (isRefresh) {
          finalTemplates = newTemplates;
        } else {
          const existingIds = new Set(this.data.templates.map(t => t.id));
          const uniqueNewTemplates = newTemplates.filter(template => !existingIds.has(template.id));
          finalTemplates = [...this.data.templates, ...uniqueNewTemplates];

          // 如果过滤掉了重复数据，记录日志
          if (uniqueNewTemplates.length < newTemplates.length) {
            console.warn(`过滤掉 ${newTemplates.length - uniqueNewTemplates.length} 个重复模板`);
          }
        }

        const total = response.total || 0;
        const currentTotal = finalTemplates.length;
        const hasMore = response.has_more !== undefined ? response.has_more : (currentTotal < total);

        this.setData({
          templates: finalTemplates,
          hasMore: hasMore,
          skip: isRefresh ? newTemplates.length : this.data.skip + (finalTemplates.length - this.data.templates.length),
          isLoading: false,
          isRefreshing: false
        });

        if (isRefresh) {
          wx.stopPullDownRefresh();
          if (newTemplates.length > 0) {
            wx.showToast({
              title: '刷新成功',
              icon: 'success',
              duration: 1500
            });
          }
        }

        console.log('模板加载完成:', {
          新增数量: isRefresh ? newTemplates.length : (finalTemplates.length - this.data.templates.length),
          总数量: finalTemplates.length,
          还有更多: hasMore
        });

      } else {
        throw new Error('获取模板列表失败');
      }
    } catch (error) {
      console.error('加载模板列表失败:', error);

      this.setData({
        isLoading: false,
        isRefreshing: false
      });

      if (isRefresh) {
        wx.stopPullDownRefresh();
      }

      let errorMessage = '网络连接失败，请检查网络';
      if (error.message) {
        if (error.message.includes('timeout')) {
          errorMessage = '请求超时，请检查网络';
        } else if (error.message.includes('网络')) {
          errorMessage = '网络连接失败，请检查网络';
        } else if (error.message.includes('500')) {
          errorMessage = '服务器内部错误，请联系管理员';
        }
      }

      wx.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 3000
      });
    }
  };

  return page;
}

// 测试用例
async function runTests() {
  console.log('=== 开始简历样式页面简化版下拉刷新测试 ===\n');

  // 测试1: 正常刷新场景
  console.log('测试1: 正常刷新场景');
  global.testScenario = 'success';
  const page1 = createMockPage();

  // 先加载初始数据
  await page1.loadTemplates();
  console.log('初始加载后模板数量:', page1.data.templates.length);

  // 执行下拉刷新
  await page1.loadTemplates(true);
  console.log('刷新后模板数量:', page1.data.templates.length);
  console.log('测试1结果:', page1.data.templates.length === 6 ? '✅ 通过' : '❌ 失败');
  console.log('');

  // 测试2: 刷新时API返回空数据（简化版本：直接用服务器返回的数据）
  console.log('测试2: 刷新时API返回空数据');
  global.testScenario = 'success';
  const page2 = createMockPage();

  // 先加载初始数据
  await page2.loadTemplates();
  console.log('初始加载后模板数量:', page2.data.templates.length);

  // 切换到空数据场景并刷新
  global.testScenario = 'empty';
  await page2.loadTemplates(true);
  console.log('刷新后模板数量:', page2.data.templates.length);
  console.log('测试2结果:', page2.data.templates.length === 0 ? '✅ 通过（使用服务器返回的数据）' : '❌ 失败');
  console.log('');

  // 测试3: 刷新时API调用失败
  console.log('测试3: 刷新时API调用失败');
  global.testScenario = 'success';
  const page3 = createMockPage();

  // 先加载初始数据
  await page3.loadTemplates();
  console.log('初始加载后模板数量:', page3.data.templates.length);

  // 切换到错误场景并刷新
  global.testScenario = 'error';
  await page3.loadTemplates(true);
  console.log('刷新后模板数量:', page3.data.templates.length);
  console.log('测试3结果:', page3.data.templates.length === 6 ? '✅ 通过（保持原有数据）' : '❌ 失败');
  console.log('');

  console.log('=== 测试完成 ===');
  console.log('简化版本说明：');
  console.log('- 正常情况下直接使用服务器返回的数据');
  console.log('- 如果服务器返回空数据，页面也显示空数据（这是正常的）');
  console.log('- 只有在网络错误等异常情况下才保持原有数据');
}

// 运行测试
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  runTests,
  createMockPage,
  mockResumeStyleApi
};
