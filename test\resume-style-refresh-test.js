/**
 * 简历样式页面下拉刷新功能测试
 * 测试修复后的下拉刷新功能，确保数据不会丢失
 */

// 模拟微信小程序环境
const mockWx = {
  showToast: (options) => {
    console.log('Toast:', options.title);
  },
  stopPullDownRefresh: () => {
    console.log('停止下拉刷新');
  }
};

// 模拟全局wx对象
global.wx = mockWx;

// 模拟API响应
const mockApiResponses = {
  // 成功响应
  success: {
    code: 200,
    data: {
      templates: [
        { id: 'template1', thumb_url: 'http://example.com/1.jpg' },
        { id: 'template2', thumb_url: 'http://example.com/2.jpg' },
        { id: 'template3', thumb_url: 'http://example.com/3.jpg' },
        { id: 'template4', thumb_url: 'http://example.com/4.jpg' },
        { id: 'template5', thumb_url: 'http://example.com/5.jpg' },
        { id: 'template6', thumb_url: 'http://example.com/6.jpg' }
      ],
      total: 6,
      skip: 0,
      limit: 20,
      has_more: false
    }
  },
  // 空响应
  empty: {
    code: 200,
    data: {
      templates: [],
      total: 0,
      skip: 0,
      limit: 20,
      has_more: false
    }
  },
  // 错误响应
  error: null
};

// 模拟API模块
const mockResumeStyleApi = {
  getResumeStyleList: async (params) => {
    console.log('API调用参数:', params);
    
    // 模拟不同的响应场景
    const scenario = global.testScenario || 'success';
    
    switch (scenario) {
      case 'success':
        return mockApiResponses.success;
      case 'empty':
        return mockApiResponses.empty;
      case 'error':
        throw new Error('网络连接失败');
      default:
        return mockApiResponses.success;
    }
  }
};

// 模拟页面对象
function createMockPage() {
  const pageData = {
    templates: [],
    selectedTemplateId: '',
    selectedTemplate: null,
    isLoading: false,
    isRefreshing: false,
    isLoadingMore: false,
    hasMore: true,
    skip: 0,
    limit: 20,
    currentCategory: '',
    currentSort: 'popular'
  };

  const page = {
    data: pageData,
    setData: function(newData) {
      Object.assign(this.data, newData);
      console.log('页面数据更新:', Object.keys(newData));
    }
  };

  // 引入loadTemplates方法（简化版本）
  page.loadTemplates = async function(isRefresh = false) {
    if (this.data.isLoading && !isRefresh) return;

    // 保存当前数据，用于刷新失败时的回滚
    const currentTemplates = isRefresh ? [...this.data.templates] : [];

    try {
      this.setData({
        isLoading: !isRefresh,
        isRefreshing: isRefresh
      });

      const skip = isRefresh ? 0 : this.data.skip;
      const params = {
        skip: skip,
        limit: this.data.limit,
        sort: this.data.currentSort
      };

      console.log('加载模板参数:', params);

      const response = await mockResumeStyleApi.getResumeStyleList(params);
      console.log('模板列表响应:', response);

      if (response && response.data && Array.isArray(response.data.templates)) {
        const newTemplates = response.data.templates;
        
        // 验证返回的数据是否有效
        if (newTemplates.length === 0 && isRefresh && currentTemplates.length > 0) {
          console.warn('刷新时获取到空数据，保留原有数据');
          throw new Error('刷新获取到空数据');
        }
        
        // 处理模板数据
        const processedTemplates = newTemplates.map((template, index) => ({
          ...template,
          isLoading: true,
          imageError: false,
          uniqueKey: `${template.id}_${Date.now()}_${index}_${Math.random().toString(36).substring(2, 11)}`
        }));

        let finalTemplates;
        if (isRefresh) {
          if (processedTemplates.length > 0) {
            finalTemplates = processedTemplates;
          } else {
            finalTemplates = currentTemplates;
            console.warn('刷新时未获取到有效数据，保留原有数据');
          }
        } else {
          const existingIds = new Set(this.data.templates.map(t => t.id));
          const uniqueNewTemplates = processedTemplates.filter(template => !existingIds.has(template.id));
          finalTemplates = [...this.data.templates, ...uniqueNewTemplates];
        }

        const total = response.data.total || 0;
        const hasMore = response.data.has_more !== undefined ? response.data.has_more : false;

        this.setData({
          templates: finalTemplates,
          hasMore: hasMore,
          skip: isRefresh ? (processedTemplates.length > 0 ? processedTemplates.length : currentTemplates.length) : this.data.skip + (finalTemplates.length - this.data.templates.length),
          isLoading: false,
          isRefreshing: false
        });

        if (isRefresh) {
          wx.stopPullDownRefresh();
          if (processedTemplates.length > 0) {
            wx.showToast({
              title: '刷新成功',
              icon: 'success',
              duration: 1500
            });
          } else if (currentTemplates.length > 0) {
            wx.showToast({
              title: '数据已是最新',
              icon: 'none',
              duration: 1500
            });
          }
        }

        console.log('模板加载完成:', {
          新增数量: isRefresh ? (processedTemplates.length > 0 ? processedTemplates.length : 0) : (finalTemplates.length - this.data.templates.length),
          总数量: finalTemplates.length,
          还有更多: hasMore,
          是否刷新: isRefresh,
          是否保留原数据: isRefresh && processedTemplates.length === 0 && currentTemplates.length > 0
        });

      } else {
        throw new Error('获取模板列表失败：响应数据格式错误');
      }
    } catch (error) {
      console.error('加载模板列表失败:', error);

      // 如果是刷新操作且有原有数据，则回滚到原有数据
      if (isRefresh && currentTemplates.length > 0) {
        console.log('刷新失败，回滚到原有数据');
        this.setData({
          templates: currentTemplates,
          isLoading: false,
          isRefreshing: false
        });
      } else {
        this.setData({
          isLoading: false,
          isRefreshing: false
        });
      }

      if (isRefresh) {
        wx.stopPullDownRefresh();
      }

      let errorMessage = '网络连接失败，请检查网络';
      if (error.message && error.message.includes('空数据')) {
        errorMessage = isRefresh && currentTemplates.length > 0 ? '数据已是最新' : '暂无可用模板';
      }

      if (!isRefresh || currentTemplates.length === 0) {
        wx.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 3000
        });
      } else {
        wx.showToast({
          title: '刷新失败，数据已保留',
          icon: 'none',
          duration: 2000
        });
      }
    }
  };

  return page;
}

// 测试用例
async function runTests() {
  console.log('=== 开始简历样式页面下拉刷新测试 ===\n');

  // 测试1: 正常刷新场景
  console.log('测试1: 正常刷新场景');
  global.testScenario = 'success';
  const page1 = createMockPage();
  
  // 先加载初始数据
  await page1.loadTemplates();
  console.log('初始加载后模板数量:', page1.data.templates.length);
  
  // 执行下拉刷新
  await page1.loadTemplates(true);
  console.log('刷新后模板数量:', page1.data.templates.length);
  console.log('测试1结果:', page1.data.templates.length === 6 ? '✅ 通过' : '❌ 失败');
  console.log('');

  // 测试2: 刷新时API返回空数据
  console.log('测试2: 刷新时API返回空数据');
  global.testScenario = 'success';
  const page2 = createMockPage();
  
  // 先加载初始数据
  await page2.loadTemplates();
  const initialCount = page2.data.templates.length;
  console.log('初始加载后模板数量:', initialCount);
  
  // 切换到空数据场景并刷新
  global.testScenario = 'empty';
  await page2.loadTemplates(true);
  console.log('刷新后模板数量:', page2.data.templates.length);
  console.log('测试2结果:', page2.data.templates.length === initialCount ? '✅ 通过（数据已保留）' : '❌ 失败（数据丢失）');
  console.log('');

  // 测试3: 刷新时API调用失败
  console.log('测试3: 刷新时API调用失败');
  global.testScenario = 'success';
  const page3 = createMockPage();
  
  // 先加载初始数据
  await page3.loadTemplates();
  const initialCount3 = page3.data.templates.length;
  console.log('初始加载后模板数量:', initialCount3);
  
  // 切换到错误场景并刷新
  global.testScenario = 'error';
  await page3.loadTemplates(true);
  console.log('刷新后模板数量:', page3.data.templates.length);
  console.log('测试3结果:', page3.data.templates.length === initialCount3 ? '✅ 通过（数据已保留）' : '❌ 失败（数据丢失）');
  console.log('');

  console.log('=== 测试完成 ===');
}

// 运行测试
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  runTests,
  createMockPage,
  mockResumeStyleApi
};
