// 简历样式相关API
const apiConfig = require('../../config/apiConfig');
const request = require('./request');

/**
 * 简历样式API模块
 */
const resumeStyleApi = {
  /**
   * 获取简历样式模板列表
   * @param {Object} params - 请求参数
   * @param {number} params.skip - 跳过的记录数，用于分页，默认0
   * @param {number} params.limit - 每页返回的记录数，默认20，最大50
   * @param {string} params.category - 模板分类筛选，可选值: "business", "creative", "simple"
   * @param {string} params.sort - 排序方式，可选值: "popular", "newest", "rating"
   * @returns {Promise} 返回模板列表数据
   */
  async getResumeStyleList(params = {}) {
    try {
      console.log('=== 获取简历样式模板列表 ===');
      console.log('请求参数:', params);

      // 参数验证和默认值设置
      const requestParams = {
        skip: params.skip || 0,
        limit: Math.min(params.limit || 20, 50), // 限制最大50条
        ...(params.category && { category: params.category }),
        ...(params.sort && { sort: params.sort })
      };

      console.log('处理后的请求参数:', requestParams);

      console.log('请求URL:', apiConfig.baseUrl + apiConfig.resumeStylesUrl);

      // 发送请求
      let response;
      let isUsingMockData = false;
      try {
        response = await request.request({
          url: apiConfig.resumeStylesUrl,
          method: 'GET',
          data: requestParams,
          showLoading: false,
          showError: false,
          needAuth: false, // 简历样式列表不需要认证
          timeout: apiConfig.timeout.default
        });
        console.log('API调用成功');
      } catch (error) {
        // 如果接口调用失败，使用模拟数据进行测试
        console.warn('API调用失败，使用模拟数据:', error.message);
        isUsingMockData = true;
        response = this._getMockData(requestParams);

        // 确保模拟数据的格式正确
        if (!response || !response.data || !Array.isArray(response.data.templates)) {
          console.error('模拟数据格式错误，创建默认数据');
          response = {
            code: 200,
            data: {
              templates: [],
              total: 0,
              skip: requestParams.skip,
              limit: requestParams.limit,
              has_more: false
            }
          };
        }
      }

      console.log('API响应:', response);

      // 验证响应数据结构
      if (!response || typeof response !== 'object') {
        throw new Error('服务器返回数据格式错误');
      }

      // 兼容不同的响应格式
      let result;
      if (response.code === 200 && response.data) {
        // 标准格式: { code: 200, data: { templates: [], total: 0 } }
        result = response.data;
      } else if (response.templates) {
        // 直接返回格式: { templates: [], total: 0 }
        result = response;
      } else {
        throw new Error('响应数据格式不正确');
      }

      // 验证必要字段
      if (!Array.isArray(result.templates)) {
        console.warn('templates字段不是数组，使用空数组');
        result.templates = [];
      }

      // 数据处理和验证 - 适配新的服务端数据结构
      const processedTemplates = result.templates.map((template, index) => {
        // 根据新的服务端接口，只有id和thumb_url字段
        // 需要从id中提取模板信息
        const templateId = template.id || `template_${Date.now()}_${index}`;
        const thumbUrl = template.thumb_url || '';

        // 从ID中提取分类信息和模板名称
        let category = 'general';
        let name = '简历模板';

        if (templateId.includes('bgdy/')) {
          category = 'business';
          name = '商务表格模板';
        } else if (templateId.includes('d100/')) {
          category = 'creative';
          name = '创意设计模板';
        } else if (templateId.includes('jydy/')) {
          category = 'simple';
          name = '简约清新模板';
        }

        // 从文件名中提取更具体的名称
        const fileName = templateId.split('/').pop() || '';
        if (fileName) {
          // 移除文件扩展名
          const nameWithoutExt = fileName.replace(/\.(jpg|jpeg|png|webp)$/i, '');
          if (nameWithoutExt) {
            name = nameWithoutExt;
          }
        }

        const processedTemplate = {
          id: templateId,
          name: name,
          description: this._getCategoryDescription(category),
          thumbnail: thumbUrl,
          category: category,
          tags: this._getCategoryTags(category),
          popularity: 0, // 服务端未提供，设为默认值
          rating: 0, // 服务端未提供，设为默认值
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          is_premium: false, // 服务端未提供，设为默认值
          preview_url: '',
          // 添加UI状态字段
          isLoading: true,  // 初始化时设置为加载状态
          imageError: false // 初始化错误状态
        };

        // 验证缩略图URL
        if (processedTemplate.thumbnail && !processedTemplate.thumbnail.startsWith('http')) {
          console.warn(`模板 ${processedTemplate.id} 的缩略图URL格式可能不正确:`, processedTemplate.thumbnail);
        }

        return processedTemplate;
      });

      const finalResult = {
        templates: processedTemplates,
        total: typeof result.total === 'number' ? result.total : processedTemplates.length,
        skip: requestParams.skip,
        limit: requestParams.limit,
        has_more: result.has_more !== undefined ? Boolean(result.has_more) : 
                  (processedTemplates.length === requestParams.limit)
      };

      console.log('处理后的结果:', {
        模板数量: finalResult.templates.length,
        总数: finalResult.total,
        跳过: finalResult.skip,
        限制: finalResult.limit,
        还有更多: finalResult.has_more
      });

      return finalResult;

    } catch (error) {
      console.error('获取简历样式模板列表失败:', error);

      // 详细错误日志
      if (error.response) {
        console.error('HTTP错误响应:', {
          status: error.response.status,
          statusText: error.response.statusText,
          data: error.response.data
        });
      }

      // 根据错误类型返回不同的错误信息
      let errorMessage = '获取模板列表失败';
      if (error.message) {
        if (error.message.includes('timeout')) {
          errorMessage = '请求超时，请检查网络连接';
        } else if (error.message.includes('Network')) {
          errorMessage = '网络连接失败，请检查网络';
        } else if (error.message.includes('500')) {
          errorMessage = '服务器内部错误，请稍后重试';
        } else if (error.message.includes('404')) {
          errorMessage = '接口不存在，请联系管理员';
        }
      }

      // 抛出包含详细信息的错误
      const enhancedError = new Error(errorMessage);
      enhancedError.originalError = error;
      enhancedError.code = error.response?.status || 'UNKNOWN';
      throw enhancedError;
    }
  },

  /**
   * 根据分类获取描述信息
   * @param {string} category - 分类名称
   * @returns {string} 分类描述
   */
  _getCategoryDescription(category) {
    const descriptions = {
      'business': '适合商务、金融等正式场合的表格式简历模板',
      'creative': '适合设计师、创意工作者的个性化简历模板',
      'simple': '简洁大方，适合各行各业的简约式简历模板'
    };
    return descriptions[category] || '专业简历模板';
  },

  /**
   * 根据分类获取标签
   * @param {string} category - 分类名称
   * @returns {Array} 标签数组
   */
  _getCategoryTags(category) {
    const tags = {
      'business': ['商务', '正式', '表格'],
      'creative': ['创意', '设计', '个性'],
      'simple': ['简约', '清新', '通用']
    };
    return tags[category] || ['简历', '模板'];
  },

  /**
   * 根据分类获取描述信息
   * @param {string} category - 分类名称
   * @returns {string} 分类描述
   */
  _getCategoryDescription(category) {
    const descriptions = {
      'business': '适合商务、金融等正式场合的表格式简历模板',
      'creative': '适合设计师、创意工作者的个性化简历模板',
      'simple': '简洁大方，适合各行各业的简约式简历模板'
    };
    return descriptions[category] || '专业简历模板';
  },

  /**
   * 根据分类获取标签
   * @param {string} category - 分类名称
   * @returns {Array} 标签数组
   */
  _getCategoryTags(category) {
    const tags = {
      'business': ['商务', '正式', '表格'],
      'creative': ['创意', '设计', '个性'],
      'simple': ['简约', '清新', '通用']
    };
    return tags[category] || ['简历', '模板'];
  },

  /**
   * 获取模拟数据（用于测试）
   * @param {Object} params - 请求参数
   * @returns {Object} 模拟响应数据
   */
  _getMockData(params = {}) {
    console.log('使用模拟数据，参数:', params);

    // 固定的6个模板数据 - 匹配新的服务端数据结构（只有id和thumb_url）
    const mockTemplates = [
      {
        id: 'bgdy/商务表格01.jpg',
        thumb_url: 'https://via.placeholder.com/280x400/4B8BF5/FFFFFF?text=商务表格01'
      },
      {
        id: 'bgdy/商务表格02.jpg',
        thumb_url: 'https://via.placeholder.com/280x400/4B8BF5/FFFFFF?text=商务表格02'
      },
      {
        id: 'd100/创意设计01.jpg',
        thumb_url: 'https://via.placeholder.com/280x400/667eea/FFFFFF?text=创意设计01'
      },
      {
        id: 'd100/创意设计02.jpg',
        thumb_url: 'https://via.placeholder.com/280x400/667eea/FFFFFF?text=创意设计02'
      },
      {
        id: 'jydy/简约清新01.jpg',
        thumb_url: 'https://via.placeholder.com/280x400/28a745/FFFFFF?text=简约清新01'
      },
      {
        id: 'jydy/简约清新02.jpg',
        thumb_url: 'https://via.placeholder.com/280x400/28a745/FFFFFF?text=简约清新02'
      }
    ];

    const skip = params.skip || 0;
    const limit = params.limit || 20;

    // 确保总是返回这6个模板（第一页）
    if (skip === 0) {
      // 第一页，返回所有6个模板
      return {
        code: 200,
        data: {
          templates: mockTemplates,
          total: mockTemplates.length,
          skip: 0,
          limit: limit,
          has_more: false // 只有6个模板，没有更多数据
        }
      };
    } else {
      // 后续页面，返回空数据
      return {
        code: 200,
        data: {
          templates: [],
          total: mockTemplates.length,
          skip: skip,
          limit: limit,
          has_more: false
        }
      };
    }
  },

  /**
   * 获取简历样式模板详情
   * @param {string} templateId - 模板ID
   * @returns {Promise} 返回模板详情数据
   */
  async getResumeStyleDetail(templateId) {
    try {
      console.log('=== 获取简历样式模板详情 ===');
      console.log('模板ID:', templateId);

      if (!templateId) {
        throw new Error('模板ID不能为空');
      }

      console.log('请求URL:', `${apiConfig.baseUrl}${apiConfig.resumeStyleDetailUrl}/${templateId}`);

      // 发送请求
      const response = await request.request({
        url: `${apiConfig.resumeStyleDetailUrl}/${templateId}`,
        method: 'GET',
        showLoading: false,
        showError: false,
        needAuth: false,
        timeout: apiConfig.timeout.default
      });

      console.log('API响应:', response);

      // 验证响应数据
      if (!response || typeof response !== 'object') {
        throw new Error('服务器返回数据格式错误');
      }

      // 兼容不同的响应格式
      let result;
      if (response.code === 200 && response.data) {
        result = response.data;
      } else if (response.id) {
        result = response;
      } else {
        throw new Error('响应数据格式不正确');
      }

      console.log('模板详情获取成功:', result);
      return result;

    } catch (error) {
      console.error('获取简历样式模板详情失败:', error);

      let errorMessage = '获取模板详情失败';
      if (error.message) {
        if (error.message.includes('timeout')) {
          errorMessage = '请求超时，请检查网络连接';
        } else if (error.message.includes('404')) {
          errorMessage = '模板不存在';
        } else if (error.message.includes('500')) {
          errorMessage = '服务器内部错误，请稍后重试';
        }
      }

      const enhancedError = new Error(errorMessage);
      enhancedError.originalError = error;
      enhancedError.code = error.response?.status || 'UNKNOWN';
      throw enhancedError;
    }
  }
};

module.exports = resumeStyleApi;
