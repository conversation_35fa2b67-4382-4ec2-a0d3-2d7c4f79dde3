const app = getApp();
const ResumeFormHelper = require('../../../../utils/resume/ResumeFormHelper.js');

Page({
  data: {
    internshipEditFormData: null, // 将在 onLoad 中初始化为 InternshipItem 实例
    isEdit: false,
    editIndex: -1,
    currentDate: '' // 当前日期，用于"至今"后重新选择日期时的默认值
  },

  onLoad(options) {
    console.log('=== 实习经历编辑页面加载 ===');

    // 初始化当前日期（格式：YYYY-MM）
    const now = new Date();
    const currentDate = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
    this.setData({
      currentDate: currentDate
    });

    if (options.index) {
      // 编辑模式
      const index = parseInt(options.index);
      this.setData({
        isEdit: true,
        editIndex: index
      });
      this.loadInternshipData(index);
    } else {
      // 新增模式
      this.loadInternshipData();
    }
  },

  /**
   * 加载实习经历数据
   */
  loadInternshipData(index = -1) {
    try {
      let internshipData;

      if (index >= 0) {
        // 编辑模式：从全局管理器获取指定索引的实习经历
        const resumeManager = app.getResumeManager();
        const currentResume = resumeManager.getCurrentResume();

        if (currentResume && currentResume.internship && currentResume.internship[index]) {
          internshipData = currentResume.internship[index].toObject();
          console.log('✅ 加载实习经历数据成功:', internshipData);
        } else {
          console.warn('⚠️ 指定索引的实习经历不存在，使用空数据');
          internshipData = ResumeFormHelper.getEmptyFieldData('internshipItem');
        }
      } else {
        // 新增模式：使用空的 InternshipItem 实例
        internshipData = ResumeFormHelper.getEmptyFieldData('internshipItem');
        console.log('📝 创建空的实习经历数据:', internshipData);
      }

      this.setData({
        internshipEditFormData: internshipData
      });

    } catch (error) {
      console.error('❌ 加载实习经历数据失败:', error);
      // 出错时使用空数据
      const emptyData = ResumeFormHelper.getEmptyFieldData('internshipItem');
      this.setData({
        internshipEditFormData: emptyData
      });
    }
  },

  // 处理输入
  handleInput(e) {
    const { field } = e.currentTarget.dataset;
    this.setData({
      [`internshipEditFormData.${field}`]: e.detail.value
    });
  },

  // 处理日期选择
  handleDateChange(e) {
    const { field } = e.currentTarget.dataset;
    this.setData({
      [`internshipEditFormData.${field}`]: e.detail.value
    });
  },

  // 设置结束日期为"至今"
  setEndDateToNow() {
    this.setData({
      'internshipEditFormData.endDate': '至今'
    });
  },

  /**
   * 保存实习经历信息
   */
  saveInfo() {
    try {
      console.log('=== 保存实习经历信息 ===');
      const { internshipEditFormData, isEdit, editIndex } = this.data;
      console.log('保存的数据:', internshipEditFormData);

      // 使用 ResumeFormHelper 进行数据验证
      const errors = ResumeFormHelper.validateFieldData('internshipItem', internshipEditFormData);
      if (errors.length > 0) {
        wx.showToast({
          title: errors[0], // 显示第一个错误
          icon: 'none'
        });
        return;
      }

      // 获取当前简历的实习经历数组
      const resumeManager = app.getResumeManager();
      const currentResume = resumeManager.getCurrentResume();
      let internshipList = currentResume.internship ?
        currentResume.internship.map(item => item.toObject()) : [];

      if (isEdit && editIndex >= 0) {
        // 编辑模式：更新指定索引的数据
        internshipList[editIndex] = internshipEditFormData;
        console.log('✅ 更新实习经历，索引:', editIndex);
      } else {
        // 新增模式：添加到数组末尾
        internshipList.push(internshipEditFormData);
        console.log('✅ 新增实习经历');
      }

      // 保存整个实习经历数组
      const success = ResumeFormHelper.saveFieldData('internship', internshipList, app);

      if (success) {
        wx.showToast({
          title: '保存成功',
          icon: 'success',
          duration: 1500
        });

        console.log('✅ 实习经历保存成功');

        setTimeout(() => {
          wx.navigateBack();
        }, 500);
      } else {
        wx.showToast({
          title: '保存失败',
          icon: 'none'
        });
      }

    } catch (error) {
      console.error('❌ 保存实习经历失败:', error);
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    }
  },

  /**
   * 删除实习经历信息
   */
  deleteInfo() {
    wx.showModal({
      title: '提示',
      content: '确定要删除该实习经历吗？',
      success: (res) => {
        if (res.confirm) {
          try {
            console.log('=== 删除实习经历信息 ===');
            const { editIndex } = this.data;

            if (editIndex < 0) {
              wx.showToast({
                title: '删除失败：无效索引',
                icon: 'none'
              });
              return;
            }

            // 获取当前简历的实习经历数组
            const resumeManager = app.getResumeManager();
            const currentResume = resumeManager.getCurrentResume();
            let internshipList = currentResume.internship ?
              currentResume.internship.map(item => item.toObject()) : [];

            // 删除指定索引的数据
            internshipList.splice(editIndex, 1);

            // 保存更新后的数组
            const success = ResumeFormHelper.saveFieldData('internship', internshipList, app);

            if (success) {
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });

              console.log('✅ 实习经历删除成功，索引:', editIndex);

              setTimeout(() => {
                wx.navigateBack();
              }, 500);
            } else {
              wx.showToast({
                title: '删除失败',
                icon: 'none'
              });
            }

          } catch (error) {
            console.error('❌ 删除实习经历失败:', error);
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            });
          }
        }
      }
    });
  }
});