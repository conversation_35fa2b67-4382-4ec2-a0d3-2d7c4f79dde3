<!--pages/errorTest/errorTest.wxml-->
<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <text class="title">错误上报测试工具</text>
    <text class="subtitle">用于测试和调试错误监控系统</text>
  </view>

  <!-- 错误上报器状态 -->
  <view class="status-section">
    <view class="section-title">错误上报器状态</view>
    <view class="status-card">
      <view wx:if="{{errorReporterStatus.available}}" class="status-item">
        <text class="status-label">状态:</text>
        <text class="status-value success">正常运行</text>
      </view>
      <view wx:else class="status-item">
        <text class="status-label">状态:</text>
        <text class="status-value error">不可用</text>
      </view>
      
      <view wx:if="{{errorReporterStatus.available}}" class="status-details">
        <view class="status-item">
          <text class="status-label">队列长度:</text>
          <text class="status-value">{{errorReporterStatus.queue_length}}</text>
        </view>
        <view class="status-item">
          <text class="status-label">上报中:</text>
          <text class="status-value">{{errorReporterStatus.is_reporting ? '是' : '否'}}</text>
        </view>
        <view class="status-item">
          <text class="status-label">已启用:</text>
          <text class="status-value">{{errorReporterStatus.is_enabled ? '是' : '否'}}</text>
        </view>
      </view>
      
      <view wx:if="{{!errorReporterStatus.available}}" class="error-message">
        <text>错误: {{errorReporterStatus.error}}</text>
      </view>
    </view>
    
    <button class="refresh-btn" bindtap="refreshStatus">刷新状态</button>
  </view>

  <!-- 测试控制 -->
  <view class="control-section">
    <view class="section-title">测试控制</view>
    <view class="button-group">
      <button 
        class="test-btn primary" 
        bindtap="runAllTests" 
        disabled="{{isRunning}}"
      >
        {{isRunning ? '测试运行中...' : '运行所有测试'}}
      </button>
      
      <button class="test-btn" bindtap="clearResults">清空结果</button>
      <button class="test-btn" bindtap="clearErrorQueue">清空错误队列</button>
    </view>
  </view>

  <!-- 单项测试 -->
  <view class="individual-tests">
    <view class="section-title">单项测试</view>
    <view class="test-grid">
      <button class="test-item-btn" bindtap="testManualErrorSingle">手动错误上报</button>
      <button class="test-item-btn" bindtap="testNetworkErrorSingle">网络错误模拟</button>
      <button class="test-item-btn" bindtap="testBusinessErrorSingle">业务逻辑错误</button>
      <button class="test-item-btn" bindtap="testPermissionErrorSingle">权限错误</button>
      <button class="test-item-btn" bindtap="triggerJSError">触发JS错误</button>
      <button class="test-item-btn" bindtap="triggerPromiseRejection">Promise拒绝</button>
      <button class="test-item-btn" bindtap="simulateMemoryWarning">内存警告</button>
    </view>
  </view>

  <!-- 测试结果 -->
  <view class="results-section" wx:if="{{testResults.length > 0}}">
    <view class="section-title">测试结果</view>
    <view class="results-list">
      <view 
        wx:for="{{testResults}}" 
        wx:key="index" 
        class="result-item {{item.success ? 'success' : 'error'}}"
      >
        <view class="result-header">
          <text class="result-name">{{item.name}}</text>
          <text class="result-time">{{item.time}}</text>
        </view>
        <view class="result-status">
          <text class="status-icon">{{item.success ? '✅' : '❌'}}</text>
          <text class="result-message">{{item.message}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 使用说明 -->
  <view class="help-section">
    <view class="section-title">使用说明</view>
    <view class="help-content">
      <text class="help-text">1. 检查错误上报器状态是否正常</text>
      <text class="help-text">2. 运行所有测试或选择单项测试</text>
      <text class="help-text">3. 查看测试结果和控制台日志</text>
      <text class="help-text">4. 在服务端验证错误是否成功上报</text>
      <text class="help-text">5. 使用清空队列功能清理测试数据</text>
    </view>
  </view>
</view>
